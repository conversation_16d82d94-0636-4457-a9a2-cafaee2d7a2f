# 项目说明

## 关于项目

本项目是一个微信小程序购票软件，分为小程序部分和FastAPI后端部分。卖出的票用来到公司旗下不同的展馆体验 VR 演出和展览。主要合作方是一些博物馆或一些名胜古迹相关单位。比如国家博物馆、江西滕王阁等。

主要功能如下：

### 小程序前端功能

1. 首页展示
2. 门票列表（可以按展馆和城市筛选）
3. 联票列表
4. 查看门票（展览）详情
5. 购票（可选择场次、规格、数量）
6. 预约功能
7. 下单、支付
8. 订单功能：查看订单列表、删除历史订单、支付未完成的订单
9. 出具电子票二维码
10. 扫码核销（管理员功能）

### 后端功能

1. 提供与前端功能一一对应的 API 接口
2. 提供管理员的维护后台
    1. 管理员登录
    2. 查看近期数据
    3. 查看订单数据、用户数据、收入数据、预约列表
    4. VR 主题管理：添加、查看、修改、删除主题
    5. 展馆管理：添加、查看、修改、删除展馆
    6. 主题管理：添加、查看、修改、删除展览
    7. 全局配置
        1. 配置轮播图
        2. 配置推荐展览
        3. 配置公司介绍文案
        4. 配置退改说明
        5. 配置用户须知
        6. 配置隐私政策
    8. 支付配置
    9.  成员管理：管理后台人员的账号和权限

## 技术方案

### 微信小程序部分

- 开发语言: JavaScript
- 框架: 原生小程序框架

### FastAPI 后端部分

- 操作系统: Ubuntu 24.04 及以上版本
- 编程语言: Python 3.12 及以上版本
- Web 框架: FastAPI
- 模板引擎: Jinja2
- ORM: Tortoise ORM
- 数据库: PostgreSQL v17.5
- 缓存: Redis v7.22
- 反向代理: Nginx v1.28
- 管理后台UI库: Bootstrap v5

## 代码结构

1. 项目分为两大部分：微信小程序和FastAPI后端。
2. 下文中以 $PROJECT 代表项目跟路径
3. $PROJECT/mp 目录下的所有内容属于微信小程序的代码, 文件和目录结构完全满足微信小程序的规范：

   - `pages/`: 小程序页面
   - `assets/`: 图片等资源文件
   - `components/`: 小程序组件
   - `utils/`: 小程序工具函数
   - `custom-tab-bar`: 自定义的底部导航栏
   - `app.js`: 小程序入口文件
   - `app.wxml`: 小程序模板文件
   - `app.json`: 小程序配置文件
   - `app.wxss`: 小程序样式文件

4.  $PROJECT/srv 目录下的所有内容属于 FastAPI 的后端代码

    - `admin/`: 后台管理系统相关代码
    - `apps/`: 业务逻辑代码，提供与小程序的 API 接口以及程序的核心功能代码
    - `config/`: 配置文件
    - `libs/`: 偏底层的公共模块，如ORM封装、缓存封装、Session、中间件、状态组件、微信支付等
    - `migrations/`: aerich 的数据库迁移文件
    - `static/`: 静态文件
    - `main.py`: 程序入口
