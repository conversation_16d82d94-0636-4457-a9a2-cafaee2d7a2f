const formatNumber = (n) => {
  n = n.toString();
  return n[1] ? n : `0${n}`;
};

const isoTime = (date) => {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hour = date.getHours();
  const minute = date.getMinutes();
  const second = date.getSeconds();

  return `${[year, month, day].map(formatNumber).join('-')} ${[hour, minute, second].map(formatNumber).join(':')}`;
};

const reloadCurrentPage = () => {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const route = currentPage.route;
  const options = currentPage.options;
  const query = Object.keys(options)
    .map((k) => `${k}=${options[k]}`)
    .join('&');
  const url = `/${route}?${query}`;
  wx.redirectTo({ url });
};

const parseNotice = (text) => {
  const lines = text.split('\n').filter((line) => line.trim() !== '');
  const nodes = [];
  for (const line of lines) {
    if (line.startsWith('# ')) {
      nodes.push({
        type: 'title',
        content: line.substring(2).trim()
      });
    } else {
      const contentParts = [];
      const regex = /\*\*(.*?)\*\*/g; // 只捕获 ** 之间的内容
      let lastIndex = 0;
      let match;

      while ((match = regex.exec(line)) !== null) {
        // 添加匹配前的普通文本
        if (match.index > lastIndex) {
          contentParts.push({ type: 'normal', text: line.substring(lastIndex, match.index) });
        }
        // 添加加粗文本
        contentParts.push({ type: 'emp', text: match[1] });
        lastIndex = regex.lastIndex;
      }

      // 添加最后一个匹配项之后的剩余文本
      if (lastIndex < line.length) {
        contentParts.push({ type: 'normal', text: line.substring(lastIndex) });
      }

      nodes.push({
        type: 'content',
        parts: contentParts
      });
    }
  }
  return nodes;
};

function deg2rad(deg) {
  return deg * (Math.PI / 180);
}

// Haversine formula to calculate distance between two points on Earth
function getDistance(lat1, lon1, lat2, lon2) {
  const R = 6371; // Radius of the earth in km
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) * Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const d = R * c; // Distance in km
  return d;
}

export default {
  isoTime,
  reloadCurrentPage,
  parseNotice,
  getDistance
};