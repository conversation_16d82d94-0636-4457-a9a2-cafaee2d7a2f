// app.js
App({
  globalData: {
    debug: false,
    userInfo: null,
    phone: null
  },

  onLaunch() {
    console.log('App Launch');
    const um = wx.getUpdateManager();
    um.onCheckForUpdate((res) => {
      if (!res.hasUpdate) return;
      um.onUpdateReady(() => {
        wx.showModal({
          title: '重要更新',
          content: '检测到小程序有新版本发布，更新后方可继续使用。',
          showCancel: false,
          confirmText: '立即更新',
          success: () => um.applyUpdate()
        });
      });
      um.onUpdateFailed(() => {
        wx.showModal({
          title: '更新失败',
          content: '新版本下载失败，请删除小程序后重新进入',
          showCancel: false
        });
      });
    });
  },

  // 设置全局数据
  setData(key, value) {
    this.globalData[key] = value;
  },

  // 获取全局数据
  getData(key) {
    return this.globalData[key];
  }
});
