<view class="container">
  <navbar transparent />
  <view class="section">
    <block wx:for="{{notice}}" wx:key="index">
      <view wx:if="{{item.type === 'title'}}" class="title">{{item.content}}</view>
      <view wx:if="{{item.type === 'content'}}" class="content">
        <text wx:for="{{item.parts}}" wx:for-item="part" wx:key="partIndex" class="{{part.type === 'emp' ? 'emp' : ''}}">{{part.text}}</text>
      </view>
    </block>
  </view>
</view>
