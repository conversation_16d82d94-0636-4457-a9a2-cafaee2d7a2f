<view class="container">
  <!-- 头像背景 -->
  <view class="main-banner"></view>

  <!-- 手机号输入对话框 -->
  <view class="phone-dialog {{ showPhoneDialog ? 'show' : '' }}">
    <view class="dialog-content">
      <view class="dialog-title">请先绑定手机</view>
      <input type="number"
             class="phone-input"
             maxlength="11"
             placeholder="请输入11位手机号"
             bindinput="onPhoneInput"
             value="{{ inputPhone }}" />
      <view class="dialog-desc">此手机号将作为观展时的验票凭证，请勿填错</view>
      <view class="dialog-buttons">
        <button class="btn cancel" bindtap="closePhoneDialog">取消</button>
        <button class="btn {{ isValidPhone ? '' : 'disabled' }}"
                bindtap="submitPhone"
                disabled="{{ !isValidPhone }}">确定</button>
      </view>
    </view>
  </view>

  <!-- 菜单区 -->
  <view class="main-card">
    <!-- 个人资料区 -->
    <view class="profile-section">
      <button  class="avatar-wrapper" open-type="chooseAvatar" bind:chooseavatar="setUserAvatar">
        <image wx:if="{{ avatar }}" class="avatar" src="{{ avatar }}"></image>
        <image wx:else class="default-avatar" src="/assets/icons/user-solid.svg"></image>
      </button>

      <text class="nickname">{{ nickname }}</text>
    </view>

    <view class="menu-section">
      <view wx:if="{{ !phone }}" class="menu-item">
        <button open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber">
          绑定手机
        </button>
        <image class="arrow-right" src="/assets/icons/right-solid.svg"></image>
      </view>
      <view class="menu-item" bindtap="goToUserOrders">
        <text>我的订单</text>
        <image class="arrow-right" src="/assets/icons/right-solid.svg"></image>
      </view>
      <view class="menu-item" bindtap="goToThirdpartyOrders">
        <text>渠道预约订单</text>
        <image class="arrow-right" src="/assets/icons/right-solid.svg"></image>
      </view>
      <view class="menu-item" bindtap="goToUserNotice">
        <text>用户须知</text>
        <image class="arrow-right" src="/assets/icons/right-solid.svg"></image>
      </view>
      <view class="menu-item" bindtap="goToUserPrivacy">
        <text>隐私政策</text>
        <image class="arrow-right" src="/assets/icons/right-solid.svg"></image>
      </view>
      <view wx:if="{{ phone }}" class="menu-item" bindtap="unbindPhone">
        <text>解绑手机</text>
        <image class="arrow-right" src="/assets/icons/right-solid.svg"></image>
      </view>
    </view>
  </view>
</view>
