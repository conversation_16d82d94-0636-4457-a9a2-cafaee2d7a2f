@import '/app.wxss';

.main-banner {
  height: 30vh;
  background: linear-gradient(150deg, var(--tertiary) 0%, var(--primary) 100%);
}

.main-card {
  background-color: var(--paper);
}

.profile-section {
  position: relative;
  display: flex;
  margin-top: -160rpx;
  align-items: center;
  flex-direction: column;
}

.avatar-wrapper {
  display: flex;
  width: 210rpx;
  height: 210rpx;
  max-width: 210rpx;
  border-radius: 50%;
  overflow: hidden;
  background-color: var(--tertiary);
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 0;
  box-shadow: 0 -5rpx 5rpx var(--shadow);
}

.avatar {
  width: 100%;
  height: 100%;
}

.default-avatar {
  width: 180rpx;
  height: 180rpx;
  padding-top: 50rpx;
}

.nickname {
  font-size: 37rpx;
  font-weight: 700;
  color: var(--primary);
}

.menu-section {
  width: 720rpx;
  margin: 10rpx auto;
  border-radius: 25rpx;
  overflow: hidden;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx;
  margin: 30rpx 5rpx;
  background-color: white;
  border-radius: 25rpx;
  box-shadow: 0 0 5rpx var(--bright);
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item text,
.menu-item button {
  font-size: 32rpx;
  color: var(--dark);
  font-weight: 400;
  flex: 1;
  background-color: white;
}

.menu-item button {
  width: fit-content;
  margin: 0;
  padding: 0;
  text-align: left;
  line-height: 1;
}

.arrow-right {
  width: 30rpx;
  height: 30rpx;
}

.phone-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: none;
  justify-content: center;
  align-items: center;
}

.phone-dialog.show {
  display: flex;
}

.dialog-content {
  background-color: #fff;
  width: 80%;
  max-width: 600rpx;
  border-radius: 20rpx;
  padding: 30rpx;
}

.dialog-title {
  font-size: 32rpx;
  text-align: center;
  margin-bottom: 30rpx;
  font-weight: 500;
}

.dialog-desc {
  margin: 0 0 25rpx 10rpx;
  color: var(--medium);
  font-size: 0.9rem;
}

.phone-input {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  margin-bottom: 30rpx;
  font-size: 28rpx;
}

.dialog-buttons {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.btn.cancel {
  background-color: var(--bright);
  color: var(--medium);
}

.btn.disabled {
  background-color: var(--secondary) !important;
}
