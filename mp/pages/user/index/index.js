import api from '../../../utils/http';

Page({
  data: {
    nickname: '未登录', // 用于展示的昵称
    avatar: null,
    name: null,
    phone: null,
    isAdm: false,
    showPhoneDialog: false,
    inputPhone: '',
    isValidPhone: false
  },

  onShow: async function () {
    // 更新 Docker 栏选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({ selected: 4 });
    }

    if (this.data.nickname === '未登录') {
      const avatar = wx.getStorageSync('avatar') || null;
      const user = await api.getUser();

      this.setData({
        avatar: avatar,
        name: user.name,
        phone: user.phone,
        isAdm: user.isAdm
      });

      this.setNickname();
    }
  },

  setNickname: function () {
    const adm = this.data.isAdm ? '👑' : '';
    if (this.data.phone) {
      this.setData({ nickname: `${adm} 手机用户 ${this.data.phone.slice(7)}` });
    } else if (this.data.name) {
      this.setData({ nickname: `${adm} ${this.data.name}` });
    } else {
      this.setData({ nickname: '未登录' });
    }
  },

  setUserAvatar(e) {
    this.setData({ avatar: e.detail.avatarUrl });
    wx.setStorageSync('avatar', e.detail.avatarUrl);
  },

  // NOTE: 已废弃，暂时保留代码
  getUserProfile(e) {
    wx.getUserProfile({
      desc: '用于完善会员资料',
      success: (res) => {
        api.getProfile(res.encryptedData, res.iv).then((profile) => {
          this.setData({ name: profile.name, avatar: profile.avatar });
          this.setNickname();
        });
      }
    });
  },

  goToUserOrders: function () {
    wx.navigateTo({
      url: '/pages/order/index/index'
    });
  },

  goToThirdpartyOrders: function () {
    wx.showToast({
      title: '即将开放，敬请期待！',
      icon: 'none'
    });
  },

  // 导航到用户须知页面
  goToUserNotice: function () {
    wx.navigateTo({
      url: '/pages/user/notice/notice'
    }).catch((res) => {
      console.log(res);
    });
  },

  goToUserPrivacy: function () {
    wx.navigateTo({
      url: '/pages/user/privacy/privacy'
    });
  },

  // 获取手机号
  async getPhoneNumber(e) {
    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      const res = await api.getUserPhone(e.detail.encryptedData, e.detail.iv, e.detail.code);
      this.setData({
        phone: res.phone,
        isAdm: res.isAdm
      });
      this.setNickname();
    } else {
      const app = getApp();
      if (app.getData('debug') === true) {
        // 显示手机号输入对话框
        this.setData({
          showPhoneDialog: true,
          inputPhone: '',
          isValidPhone: false
        });
      }
    }
  },

  // 手机号输入处理
  onPhoneInput(e) {
    const phone = e.detail.value;
    // 验证手机号是否为11位数字
    const isValid = /^1\d{10}$/.test(phone);
    this.setData({
      inputPhone: phone,
      isValidPhone: isValid
    });
  },

  // 关闭手机号输入对话框
  closePhoneDialog() {
    this.setData({
      showPhoneDialog: false,
      inputPhone: '',
      isValidPhone: false
    });
  },

  // 提交手动输入的手机号
  async submitPhone() {
    if (this.data.isValidPhone) {
      const phone = this.data.inputPhone;
      this.setData({ phone: phone, showPhoneDialog: false });
      const res = await api.bindUserPhone(phone);
      this.setData({
        phone: res.phone,
        isAdm: res.isAdm
      });
      this.setNickname();
    }
  },

  // 解除绑定手机号
  unbindPhone() {
    wx.showModal({
      title: '解绑确认',
      content: '解除绑定手机后将无法购票，确定要解绑吗？',
      showCancel: true,
      confirmText: '解绑',
      success: async (res) => {
        if (res.confirm) {
          await api.unbindUserPhone();
          this.setData({ phone: null, isAdm: false });
          this.setNickname();
        }
      }
    });
  }
});
