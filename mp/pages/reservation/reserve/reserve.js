import api from '../../../utils/http';

Page({
  data: {
    ticket: null,
    selectedDate: '', // 当前选择的日期类型
    isoDate: '', // ISO格式的日期
    displayDate: '', // 显示的日期文本
    reservationCount: 1, // 预约人数
    showDatePicker: false, // 是否显示日期选择器
    currentMonth: '2025年5月',
    calendarDays: [] // 日历天数数据
  },

  onLoad(options) {
    // 从URL参数获取展览ID
    const oid = options.id;
    if (oid) {
      api.getOrderDetail(oid).then((res) => {
        this.setData({ ticket: res });
      });
    }

    // 初始化日期
    this.initializeDates();
    // 生成日历数据
    this.generateCalendar();
  },

  // 初始化日期
  initializeDates() {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);
    const dayAfter = new Date(today);
    dayAfter.setDate(today.getDate() + 2);

    // 检查结束日期
    if (this.data.ticket && this.data.ticket.end) {
      this.endDate = new Date(this.data.ticket.end);
    }

    // 默认选择今天
    this.setData({
      selectedDate: 'today',
      displayDate: this.formatDate(today),
      isoDate: `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`,
      todayStr: this.formatDate(today),
      tomorrowStr: this.formatDate(tomorrow),
      dayAfterStr: this.formatDate(dayAfter)
    });
  },

  // 格式化日期
  formatDate(date) {
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${month}月${day}日`;
  },

  // 选择日期
  selectDate(e) {
    const dateType = e.currentTarget.dataset.date;
    const today = new Date();
    let dateObj;
    let displayDate;

    switch (dateType) {
      case 'today':
        dateObj = today;
        displayDate = this.formatDate(today);
        break;
      case 'tomorrow':
        dateObj = new Date(today);
        dateObj.setDate(today.getDate() + 1);
        displayDate = this.formatDate(dateObj);
        break;
      case 'dayafter':
        dateObj = new Date(today);
        dateObj.setDate(today.getDate() + 2);
        displayDate = this.formatDate(dateObj);
        break;
    }

    const isoDate = `${dateObj.getFullYear()}-${String(dateObj.getMonth() + 1).padStart(2, '0')}-${String(dateObj.getDate()).padStart(2, '0')}`;
    this.setData({ selectedDate: dateType, displayDate, isoDate });
  },

  // 显示日期选择器
  showDatePicker() {
    this.setData({ showDatePicker: true });
  },

  // 隐藏日期选择器
  hideDatePicker() {
    this.setData({ showDatePicker: false });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击弹窗内容时关闭弹窗
  },

  // 生成日历数据
  generateCalendar() {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth(); // 当前月份 (0-based)
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);

    // 调整到周一开始
    const dayOfWeek = firstDay.getDay();
    const mondayOffset = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
    startDate.setDate(firstDay.getDate() - mondayOffset);

    const calendarDays = [];
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // 生成6周的日期
    for (let i = 0; i < 42; i++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);

      const isCurrentMonth = currentDate.getMonth() === month;
      const isPastDate = currentDate < today;
      const isFutureEndDate = this.endDate && currentDate > this.endDate;

      const dateStr = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(currentDate.getDate()).padStart(2, '0')}`;

      calendarDays.push({
        date: dateStr,
        day: currentDate.getDate(),
        disabled: !isCurrentMonth || isPastDate || isFutureEndDate,
        selected: false
      });
    }

    const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];

    this.setData({
      calendarDays: calendarDays,
      currentMonth: `${year}年${monthNames[month]}`
    });
  },

  // 选择日历日期
  selectCalendarDate(e) {
    const selectedDate = e.currentTarget.dataset.date;
    // 解析日期，确保日期正确
    const [year, month, day] = selectedDate.split('-').map(Number);
    // 构建本地日期时间
    const date = new Date(year, month - 1, day, 12, 0, 0); // 使用中午12点来避免任何时区问题

    // 更新日历显示
    const calendarDays = this.data.calendarDays.map((day) => ({
      ...day,
      selected: day.date === selectedDate
    }));

    this.setData({
      calendarDays: calendarDays,
      selectedDate: 'custom',
      isoDate: selectedDate,
      displayDate: `${month}月${day}日`, // 直接使用解析出来的月和日
      showDatePicker: false
    });
  },

  // 减少人数
  decreaseCount() {
    if (this.data.reservationCount > 1) {
      this.setData({
        reservationCount: this.data.reservationCount - 1
      });
    }
  },

  // 增加人数
  increaseCount() {
    this.setData({
      reservationCount: this.data.reservationCount + 1
    });
  },

  // 确认预约
  reserveConfirm() {
    const date = this.data.isoDate;
    if (!date) {
      wx.showToast({
        title: '请选择预约日期',
        icon: 'none'
      });
    } else {
      // 提交预约
      api.confirmReserve(this.data.ticket.id, date);
      wx.showToast({
        title: '预约信息已提交',
        icon: 'success'
      });

      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  }
});
