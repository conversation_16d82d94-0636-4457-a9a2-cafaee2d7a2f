import api from '../../../utils/http';

Page({
  data: {
    ticket: {}
  },

  onLoad(options) {
    // 从URL参数获取展览ID
    const ticketId = options.id;
    if (ticketId) {
      api.getTicketDetail(ticketId).then((res) => {
        this.setData({ ticket: res });
      });
    }
  },

  // 查看详情
  goToDetail: function (e) {
    const ticketId = this.data.ticket.id;
    wx.navigateTo({ url: `/pages/ticket/exhib/exhib?id=${ticketId}` });
  },

  // 选择小程序购票预约
  selectMiniProgramReservation() {
    if (this.data.ticket) {
      wx.navigateTo({
        url: `/pages/order/purchase/purchase?id=${this.data.ticket.id}&type=reserve`
      });
    } else {
      wx.showToast({ title: '请先选择展览', icon: 'none' });
    }
  },

  // 选择其它渠道预约
  selectOtherChannelReservation() {
    api.getSettings().then((res) => {
      if (!res.reserve3th_enable) {
        wx.showToast({
          title: '此功能暂未开放，敬请期待',
          icon: 'none'
        });
        return;
      } else if (this.data.ticket) {
        wx.navigateTo({
          url: `/pages/reservation/reserve/reserve?id=${this.data.ticket.id}`
        });
      } else {
        wx.showToast({
          title: '请先选择展览',
          icon: 'none'
        });
      }
    });
  }
});
