import Cache from '../../../utils/cache';
import api from '../../../utils/http';

Page({
  data: {
    navBarTop: 0,
    navBarHeight: 0,
    logo: '/assets/logo.png',
    slides: [],
    exhiRcmd: []
  },

  async onLoad() {
    // 获取导航栏高度
    const menuBtn = wx.getMenuButtonBoundingClientRect();
    this.setData({
      navBarTop: menuBtn.top,
      navBarHeight: menuBtn.height
    });

    await this.fetchSlids();

    // 获取推荐数据
    const rcmds = await api.getExhiRcmd();
    this.setData({ exhiRcmd: rcmds });
  },

  onShow() {
    // 更新 Docker 栏选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({ selected: 0 });
    }
  },

  // 获取Logo和轮播图地址
  async fetchSlids() {
    const settings = await api.getSettings();
    if (settings) {
      if (settings.logo) this.setData({ logo: settings.logo });
      if (settings.slides) this.setData({ slides: settings.slides });
    }
  },

  goToTicketIndex() {
    Cache.set('selectedTicketTab', 'digital_exhibition', 3);
    wx.switchTab({ url: '/pages/ticket/index/index' });
  },

  goToJointIndex() {
    Cache.set('selectedTicketTab', 'joint_ticket', 3);
    wx.switchTab({ url: '/pages/ticket/index/index' });
  },

  goToTicketDetail(event) {
    const ticketId = event.currentTarget.dataset.id;
    wx.navigateTo({ url: `/pages/ticket/exhib/exhib?id=${ticketId}` });
  },

  goToAR: function () {
    wx.showToast({
      title: 'AR功能开发中，敬请期待',
      icon: 'none'
    });
  },

  goToBrand() {
    wx.navigateTo({
      url: '/pages/home/<USER>/brand'
    });
  },

  onShareAppMessage() {
    return {
      title: '欢迎来到「华夏漫游」',
      path: '/pages/home/<USER>/index',
      imageUrl: this.data.slides[0]
    };
  }
});
