import api from '../../../utils/http';
import drawQrcode from '../../../utils/weapp.qrcode.min.js';

Page({
  data: {
    orderId: '',
    checkin: null,
    detail: null,
    period: '常驻展',
    needReserve: false,
    loading: true
  },

  onLoad(options) {
    if (options.orderId) {
      const params = { orderId: options.orderId };
      if (options.checkin) params.checkin = options.checkin;
      this.setData(params);
    } else {
      wx.showToast({ title: '订单ID不能为空', icon: 'none' });
      setTimeout(() => {
        wx.navigateBack();
      }, 3000);
    }
  },

  onShow() {
    this.loadOrderDetail();
  },

  // 加载订单详情
  async loadOrderDetail() {
    try {
      this.setData({ loading: true });
      const detail = await api.getOrderDetail(this.data.orderId);
      const needReserve = detail.catg === '预约票' && !detail.ap_time ? true : false;
      this.setData({ detail: detail, loading: false, needReserve: needReserve });
      this.updatePeriod();
      // 检查是否是预约订单，如果未预约，弹窗提示，并引导预约
      if (needReserve) {
        wx.showModal({
          title: '提示',
          content: '您尚未预约观展时间。',
          showCancel: true,
          confirmText: '立即预约',
          success: (res) => {
            if (res.confirm) {
              wx.navigateTo({
                url: `/pages/reservation/reserve/reserve?id=${this.data.detail.id}`
              });
            }
          }
        });
      } else {
        drawQrcode({
          width: this.rpxToPx(240),
          height: this.rpxToPx(240),
          canvasId: 'ticketQrcode',
          text: detail.vcode || '',
          foreground: this.data.checkin || detail.status === '待使用' ? '#000000' : '#EEE'
        });
      }
    } catch (error) {
      wx.showToast({ title: '订单详情加载失败', icon: 'none' });
      this.setData({ loading: false });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  updatePeriod() {
    const start = this.data.detail.start || null;
    const end = this.data.detail.end || null;
    if (start && end) {
      this.setData({ period: `${start} ～ ${end}` });
    } else if (!start && end) {
      this.setData({ period: `${end} 为止` });
    } else {
      this.setData({ period: '常驻展' });
    }
  },

  rpxToPx(rpx) {
    const systemInfo = wx.getWindowInfo();
    return (systemInfo.windowWidth / 750) * rpx;
  },

  onPullDownRefresh() {
    this.loadOrderDetail().then(() => {
      wx.stopPullDownRefresh();
    });
  }
});
