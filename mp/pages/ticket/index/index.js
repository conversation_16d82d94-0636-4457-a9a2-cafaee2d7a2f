import Cache from '../../../utils/cache';
import api from '../../../utils/http';
import util from '../../../utils/util';

Page({
  data: {
    theaterEnable: false,
    hasAutoSelectedCity: false,
    location: {},
    isLoadingMore: false,

    // 城市选择器相关状态
    cityPicker: {
      selectedCity: '全国',
      showCityPicker: false,
      searchText: '',
      cities: [],
      groupedCities: [],
      alphabet: [],
      scrollToLetter: ''
    },

    // 票务列表相关数据
    ticketList: {
      activeNav: 'digital_exhibition',
      activeSubNavItem: '全部',
      subNavBarItems: [],
      allTickets: [],
      tickets: []
    }
  },

  onLoad: function (options) {
    const getSettings = api.getSettings().then((res) => {
      if (res.theater_enable) this.setData({ theaterEnable: true });
    });

    const getLocation = new Promise((resolve) => {
      wx.getFuzzyLocation({
        type: 'wgs84',
        success: (res) => this.setData({ location: { lat: res.latitude, lng: res.longitude } }),
        fail: () => console.log('获取用户位置失败'),
        complete: resolve
      });
    });

    Promise.all([getSettings, getLocation]);
  },

  onShow() {
    // 更新 Docker 栏选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({ selected: 1 });
    }

    // 获取选中的 Tab
    const tab = Cache.get('selectedTicketTab');
    if (tab && tab !== this.data.ticketList.activeNav) {
      this.changeNavTo(tab);
    } else if (this.data.ticketList.tickets.length === 0) {
      this.changeNavTo(this.data.ticketList.activeNav);
    }
  },

  // 导航栏点击事件
  onNavTap: function (e) {
    const navType = e.currentTarget.dataset.type;
    if (this.data.ticketList.activeNav !== navType) {
      this.changeNavTo(navType);
    }
  },

  // 处理子导航标签数据
  processSubNavItems: function () {
    const { allTickets } = this.data.ticketList;
    if (!allTickets) return;

    const allVenues = Array.from(new Set(allTickets.map((item) => item.venue).filter(Boolean)));
    this.setData({ 'ticketList.subNavBarItems': ['全部', ...allVenues] });
  },

  // 子导航栏点击事件
  onSubNavTap: function (e) {
    const itemName = e.currentTarget.dataset.name;
    if (this.data.ticketList.activeSubNavItem !== itemName) {
      this.changeSubNavTo(itemName);
    }
  },

  // 切换导航
  changeNavTo: function (navType) {
    if (navType && navType !== this.data.ticketList.activeNav) {
      this.setData({ 'ticketList.activeNav': navType });
    }

    let apiCall;
    this.setData({ isLoadingMore: true });
    if (navType === 'digital_exhibition') {
      apiCall = api.getTicketList();
    } else if (navType === 'joint_ticket') {
      apiCall = api.getJointList();
    } else {
      this.setData({
        'ticketList.allTickets': [],
        'ticketList.tickets': [],
        'ticketList.subNavBarItems': [],
        isLoadingMore: false
      });
      this.processCities();
      return;
    }

    apiCall
      .then((result) => {
        this.setData({ 'ticketList.allTickets': result });

        const uniqueCities = this.processCities();
        const { lat, lng } = this.data.location;

        if (lat && lng && uniqueCities && uniqueCities.size > 0 && !this.data.hasAutoSelectedCity) {
          let nearestCity = null;
          let minDistance = Infinity;

          uniqueCities.forEach((city) => {
            if (city.lat && city.lng) {
              const distance = util.getDistance(lat, lng, city.lat, city.lng);
              if (distance < minDistance) {
                minDistance = distance;
                nearestCity = city.name;
              }
            }
          });

          if (nearestCity) {
            this.setData({ 'cityPicker.selectedCity': nearestCity, hasAutoSelectedCity: true });
          }
        }

        this.processSubNavItems();
        this.applyFilters();
      })
      .finally(() => {
        this.setData({ isLoadingMore: false });
      });
  },

  // 切换子导航
  changeSubNavTo: function (itemName) {
    this.setData({ 'ticketList.activeSubNavItem': itemName });
    this.applyFilters();
  },

  // 从 allTickets 中提取城市信息
  processCities: function () {
    const { allTickets } = this.data.ticketList;
    if (!allTickets || allTickets.length === 0) {
      this.setData({
        'cityPicker.cities': [],
        'cityPicker.groupedCities': [],
        'cityPicker.alphabet': []
      });
      return null;
    }

    // 提取并去重城市，同时保存坐标信息
    const uniqueCities = new Map();
    allTickets.forEach((ticket) => {
      if (ticket.city && ticket.city.length === 2) {
        const [name, pinyin] = ticket.city;
        if (!uniqueCities.has(name)) {
          uniqueCities.set(name, {
            name,
            pinyin,
            initial: pinyin.charAt(0).toUpperCase(),
            lat: ticket.lat,
            lng: ticket.lng
          });
        }
      }
    });

    const cities = [...uniqueCities.values()].sort((a, b) => a.pinyin.localeCompare(b.pinyin));
    const grouped = {};
    cities.forEach((city) => {
      const letter = city.initial;
      if (!grouped[letter]) grouped[letter] = [];
      grouped[letter].push(city);
    });

    const groupedCities = Object.keys(grouped)
      .sort()
      .map((letter) => ({ letter, cities: grouped[letter] }));

    this.setData({
      'cityPicker.cities': cities,
      'cityPicker.groupedCities': groupedCities,
      'cityPicker.alphabet': groupedCities.map((g) => g.letter)
    });

    return uniqueCities;
  },

  openCityPicker: function () {
    this.setData({ 'cityPicker.showCityPicker': true });
  },

  closeCityPicker: function () {
    this.setData({ 'cityPicker.showCityPicker': false, 'cityPicker.searchText': '' });
    this.onSearchInput({ detail: { value: '' } });
  },

  onSearchInput: function (e) {
    const searchText = e.detail.value.trim().toLowerCase();
    this.setData({ 'cityPicker.searchText': searchText });

    const { cities } = this.data.cityPicker;
    if (!searchText) {
      this.processCities();
      return;
    }

    const filteredCities = cities.filter(
      (city) => city.name.toLowerCase().includes(searchText) || city.pinyin.toLowerCase().includes(searchText)
    );

    const grouped = {};
    filteredCities.forEach((city) => {
      const letter = city.initial;
      if (!grouped[letter]) grouped[letter] = [];
      grouped[letter].push(city);
    });

    const filteredGroupedCities = Object.keys(grouped)
      .sort()
      .map((letter) => ({ letter, cities: grouped[letter] }));

    this.setData({ 'cityPicker.groupedCities': filteredGroupedCities });
  },

  // 城市选择事件
  onCityTap: function (e) {
    const { cityname } = e.currentTarget.dataset;
    this.setData({
      'cityPicker.selectedCity': cityname === '全部城市' ? '全国' : cityname,
      'ticketList.activeSubNavItem': '全部',
      hasAutoSelectedCity: true
    });
    this.applyFilters();
    this.closeCityPicker();
  },

  // 根据当前选择的城市和展馆，过滤 allTickets 并更新 tickets
  applyFilters: function () {
    const { allTickets, activeSubNavItem } = this.data.ticketList;
    const { selectedCity } = this.data.cityPicker;
    let filteredList = allTickets;

    if (selectedCity !== '全国') {
      filteredList = filteredList.filter((ticket) => ticket.city && ticket.city[0] === selectedCity);
    }

    if (activeSubNavItem !== '全部') {
      filteredList = filteredList.filter((tk) => tk.venue === activeSubNavItem);
    }

    this.setData({ 'ticketList.tickets': filteredList });
  },

  scrollToLetter: function (e) {
    const letter = e.currentTarget.dataset.letter;
    this.setData({ 'cityPicker.scrollToLetter': 'letter-' + letter });
  },

  goToDetail: function (e) {
    const { activeNav } = this.data.ticketList;
    if (activeNav === 'digital_exhibition') {
      wx.navigateTo({ url: `/pages/ticket/exhib/exhib?id=${e.currentTarget.dataset.id}` });
    } else if (activeNav === 'space_theater') {
      wx.showToast({ title: '空间剧场暂未开放，敬请期待', icon: 'none' });
    } else {
      wx.navigateTo({ url: `/pages/ticket/joint/joint?id=${e.currentTarget.dataset.id}` });
    }
  }
});
