import Cache from '../../../utils/cache';
import api from '../../../utils/http';

Page({
  // 页面的初始数据
  data: {
    jointDetail: {},
    features: {
      refundable: false, // 是否可退
      source: 'office', // 票源
      electronic: true, // 是否电子票
      invoice: true // 发票信息
    },
    showNoticeDialog: false // 控制通知弹窗显示
  },

  onLoad(options) {
    api.getJointDetail(options.id).then((joint) => {
      if (!joint) {
        wx.showToast({ title: '服务器维护中，请稍候再试', icon: 'none' });
        return;
      }

      // 保存数据、设置导航栏标题
      this.setData({ jointDetail: joint });

      // 如果notice不为空，且本次运行期间未显示过，则显示通知弹窗
      const cacheKey = `noticeShown:${joint.vid}`;
      if (joint.notice && !Cache.get(cacheKey)) {
        this.setData({ showNoticeDialog: true });
        Cache.set(cacheKey, true);
      }
    });
  },

  // 处理通知弹窗确认事件
  onNoticeDialogConfirm: function () {
    this.setData({
      showNoticeDialog: false
    });
  },

  // 导航到特征详情页
  goToFeatures: function (e) {
    wx.navigateTo({ url: '/pages/user/notice/notice' });
  },

  // 导航到关联展票的详情页
  goToTicketDetail: function (e) {
    const ticketId = e.currentTarget.dataset.id;
    wx.navigateTo({ url: `/pages/ticket/exhib/exhib?id=${ticketId}&&noAction=true` });
  },

  // 购票按钮点击事件
  goToJointPurchase: function () {
    const jk = this.data.jointDetail;
    wx.navigateTo({
      url: `/pages/order/purchase/purchase?id=${jk.id}&title=${jk.title}&type=joint`
    });
  },

  // AR按钮点击事件
  goToTicketAR: function () {
    // AR相关逻辑
    if (!this.data.jointDetail) return;
    wx.showToast({
      title: 'AR功能开发中，敬请期待',
      icon: 'none'
    });
  },

  onShareAppMessage() {
    const joint = this.data.jointDetail;
    if (!joint || !joint.id) {
      return {
        title: '欢迎来到「华夏漫游」',
        path: '/pages/home/<USER>/index'
      };
    }
    return {
      title: joint.title,
      path: `/pages/ticket/joint/joint?id=${joint.id}`,
      imageUrl: joint.banner
    };
  }
});
