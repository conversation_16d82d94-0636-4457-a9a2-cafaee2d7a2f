import Cache from '../../../utils/cache';
import api from '../../../utils/http';

Page({
  data: {
    showNoticeDialog: false, // 控制通知弹窗显示
    features: {
      refundable: false, // 是否可退
      source: 'office', // 票源
      electronic: true, // 是否电子票
      invoice: true // 发票信息
    },
    ticketDetail: {},
    enableAction: true
  },

  onLoad: function (options) {
    if (options.noAction === 'true') {
      this.setData({ enableAction: false });
    }

    api.getTicketDetail(options.id).then((ticket) => {
      if (!ticket) {
        wx.showToast({ title: '服务器维护中，请稍候再试', icon: 'none' });
        return;
      }

      // 保存数据、设置导航栏标题
      this.setData({ ticketDetail: ticket });

      // 如果notice不为空，且本次运行期间未显示过，则显示通知弹窗
      const cacheKey = `noticeShown:${ticket.vid}`;
      if (!options.disableAction && ticket.notice && !Cache.get(cacheKey)) {
        this.setData({ showNoticeDialog: true });
        Cache.set(cacheKey, true);
      }
    });
  },

  // 处理通知弹窗确认事件
  onNoticeDialogConfirm: function () {
    this.setData({
      showNoticeDialog: false
    });
  },

  // 导航到特征详情页
  goToFeatures: function (e) {
    wx.navigateTo({ url: '/pages/user/notice/notice' });
  },

  // 购票按钮点击事件
  goToTicketPurchase: function () {
    const tk = this.data.ticketDetail;
    wx.navigateTo({
      url: `/pages/order/purchase/purchase?id=${tk.id}&title=${tk.title}&type=ticket`
    });
  },

  // AR按钮点击事件
  goToTicketAR: function () {
    // AR相关逻辑
    if (!this.data.ticketDetail) return;
    wx.showToast({
      title: 'AR功能开发中，敬请期待',
      icon: 'none'
    });
  },

  onShareAppMessage() {
    const ticket = this.data.ticketDetail;
    if (!ticket || !ticket.id) {
      return {
        title: '欢迎来到「华夏漫游」',
        path: '/pages/home/<USER>/index'
      };
    }
    return {
      title: ticket.title,
      path: `/pages/ticket/exhib/exhib?id=${ticket.id}`,
      imageUrl: ticket.banner
    };
  }
});
