@import '/app.wxss';

.container {
  position: relative;
}

.ticket-info-section {
  margin-bottom: 20rpx;
}

.ticket-title {
  font-size: 40rpx;
  font-weight: bold;
  color: var(--dark);
  line-height: 1.4;
  margin-bottom: 15rpx;
}

.ticket-address {
  font-size: 26rpx;
  color: var(--medium);
  margin-bottom: 20rpx;
}

.ticket-price {
  display: flex;
  font-size: 40rpx;
  color: var(--dark);
  font-weight: bold;
  align-items: baseline;
}

.ticket-price .price-symbol {
  font-size: 36rpx;
  margin-right: 5rpx;
}

/* Ticket Features Section */
.features-section {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  margin: 20rpx auto;
  border-top: 1rpx solid var(--silver);
  border-bottom: 1rpx solid var(--silver);
}

.features-disable {
  display: flex;
  align-items: center;
  height: 1px;
  margin: 20rpx auto;
  background-color: var(--silver);
}

.feature-list {
  display: flex;
  flex-grow: 1;
  overflow-x: auto;
  white-space: nowrap;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-right: 15rpx;
}

.feature-icon {
  width: 22rpx;
  height: 22rpx;
  margin-right: 10rpx;
}

.feature-text {
  font-size: 22rpx;
  color: var(--medium);
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  margin-left: auto;
}

/* 演出详情标题区域 */
.detail-title-section {
  margin-top: 20rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: var(--dark);
  margin: 10rpx 0;
}

.notice-content {
  font-size: 26rpx;
  color: var(--medium);
  line-height: 1.6;
  white-space: pre-wrap;
}

.detail-image {
  display: block;
  width: 100%;
  margin: 0;
}

/* 底部购票按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  display: flex;
  padding: 15rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  background-color: white;
  box-shadow: 0 -2rpx 5rpx var(--shadow);
  z-index: 100;
  box-sizing: border-box;
}

.ar-btn {
  display: flex;
  flex: 0 0 30%;
  background-color: var(--lightblue);
}

.ar-btn image {
  height: 30rpx;
}

.purchase-btn {
  flex: 0 0 65%;
}

/* 加载提示 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 28rpx;
  color: var(--dark);
}
