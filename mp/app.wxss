page {
  --paper: #f2f2f2;
  --silver: #eee;
  --bright: #ccc;
  --light: #999;
  --medium: #666;
  --dark: #333;
  --lightblue: #edecff;
  --primary: #c8ac60;
  --secondary: #ddc481;
  --tertiary: #e7d5a2;
  --primary-deep: #a18843;
  --softred: #ec536a;
  --shadow: rgba(0, 0, 0, 0.1);
  --translucence: rgba(0, 0, 0, 0.5);

  background-color: var(--paper);
}

.pdb-2 {
  padding-bottom: 2rem;
}

/* 页面容器 */
.container {
  display: flex;
  width: 100%;
  min-height: 100vh;
  padding-bottom: 200rpx;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  transition: padding-top 0.35s ease-in-out;
}

.main-banner {
  background-color: var(--primary);
  width: 100%;
  box-sizing: border-box;
}

.main-card {
  position: relative;
  flex: 1;
  z-index: 10;
  margin-top: -50rpx;
  padding-bottom: 120rpx;
  border-radius: 50rpx 50rpx 0 0;
  width: 100%;
  background: linear-gradient(180deg, white, var(--paper));
  box-sizing: border-box;
  box-shadow: 0 -5rpx 5rpx var(--shadow);
}

.section {
  padding: 50rpx 50rpx 20rpx 50rpx;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  padding: 10rpx 0;
}

.content {
  font-size: 28rpx;
  color: var(--medium);
  margin-bottom: 50rpx;
  text-align: left;
  line-height: 1.8;
}

.emp {
  font-weight: 900;
  color: var(--dark);
}

.btn {
  padding: 20rpx 40rpx;
  align-items: center;
  justify-content: center;
  background-color: var(--primary);
  color: white;
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.4;
  border: 1rpx solid transparent;
  border-radius: 100rpx;
  box-sizing: border-box;
}

.btn:active {
  background-color: var(--primary-deep);
}

.btn-sm {
  font-size: 0.8rem;
  padding: 16rpx 32rpx;
  line-height: 1;
}

/* 票务信息列表 */
.item-list {
  display: flex;
  padding: 20rpx 0;
  flex-direction: column;
}

/* 票务列表项 */
.item {
  display: flex;
  background-color: white;
  width: 720rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  align-items: flex-start;
  border-radius: 20rpx;
  box-sizing: border-box;
  box-shadow: 0 0 10rpx var(--shadow);
}

/* 缩略图样式 */
.item-thumbnail {
  width: 180rpx;
  height: 240rpx;
  margin-right: 10rpx;
  border-radius: 16rpx;
}

.item-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  position: relative;
  width: 100%;
}

/* 标题样式 */
.item-title {
  display: -webkit-box;
  margin-bottom: 15rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: var(--dark);
  line-height: 1.8;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 单行简述 */
.item-desc {
  display: -webkit-box;
  margin: 5rpx 0;
  font-size: 24rpx;
  color: var(--medium);
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-spec {
  display: flex;
  width: 100%;
  margin-top: 15rpx;
  justify-content: flex-end;
}

.price {
  font-size: 24rpx;
  color: var(--primary);
  height: 100%;
  align-items: baseline;
}

.price .price-symbol {
  font-size: 28rpx;
  margin-right: 4rpx;
}

.price .price-value {
  font-size: 50rpx;
  font-weight: bold;
  margin-right: 4rpx;
}

.price .price-suffix {
  font-size: 24rpx;
  color: var(--light);
}

.empty {
  display: flex;
  flex: 1;
  padding-bottom: 200rpx;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: var(--light);
  font-size: 28rpx;
  height: 100%;
}

.empty-image {
  width: 270rpx;
  height: 270rpx;
  min-width: 200rpx;
  max-width: 375rpx;
  margin-bottom: 35rpx;
}
