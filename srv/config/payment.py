import logging
from pathlib import Path

from wechatpayv3 import WeChatPayType

from config.server import BASE_DIR, DOMAIN
from libs.attrdict import AttrDict

# 小程序配置
MP_APP_ID = 'wx4323fddc79f777b7'
MP_APP_SECRET = '4de4ac5539e321783527fecb93cc2cac'  # noqa: S105

# 回调地址
PAYMENT_NOTIFY_URL = f'https://{DOMAIN}/api/payment/callback'  # 支付回调
REFUND_NOTIFY_URL = f'https://{DOMAIN}/api/refund/callback'  # 退款回调

WX_PAYMENT = AttrDict({
    'appid': MP_APP_ID,
    'wechatpay_type': WeChatPayType.MINIPROG,  # 微信支付类型
    'mchid': '1719404015',  # 微信支付商户号(服务商户号 sp_mchid)
    # 'apiv2_key': 'qzGRjRQhReZzHDc6P1UmnyN6AkxPLzXA',  # API v2密钥
    'apiv3_key': 'zMypODhCGpzeIKjeiZseDFmSrdXioXgW',  # API v3密钥
    'private_key': Path(BASE_DIR, 'config/certs/apiclient_key.pem').read_text(),  # 商户证书私钥
    'cert_serial_no': '7825842D2D423B2AC01E4A019EFF0F9C9962A1B0',  # 商户证书序列号
    'cert_dir': f'{BASE_DIR}/config/certs',  # 微信支付平台证书目录
    'public_key': Path(BASE_DIR, 'config/certs/pub_key.pem').read_text(),  # 微信支付平台公钥
    'public_key_id': 'PUB_KEY_ID_0117194040152025070300381664000601',  # 微信支付平台公钥ID
    'notify_url': PAYMENT_NOTIFY_URL,  # 回调地址
    'logger': logging.getLogger('payment'),
    'partner_mode': True,  # 接入模式：False=直连商户模式, True=服务商模式.
    'timeout': (10, 30),  # 请求超时时间配置, 建立连接最大超时时间是10s, 读取响应的最大超时时间是30s
})
