import datetime
import random

from tortoise import Tortoise, run_async

from apps.order.models import Order
from apps.order.schemas import OrderStatus
from config.server import DATABASE, TZ


async def fix_timestamps_and_distribute():
    """修正所有订单的时间戳并将其分散到过去三个月内"""
    print('开始修正并分散所有订单的时间戳...')
    orders = await Order.all()
    print(f'找到了 {len(orders)} 个订单需要处理。')

    now = datetime.datetime.now(TZ)
    three_months_ago = now - datetime.timedelta(days=90)
    total_seconds_in_period = (now - three_months_ago).total_seconds()

    for order in orders:
        # 1. 生成一个新的、随机的创建时间
        random_seconds_ago = random.uniform(0, total_seconds_in_period)
        new_created_time = now - datetime.timedelta(seconds=random_seconds_ago)
        order.created = new_created_time

        # 2. 重置所有可能不一致的时间戳
        order.payment_time = None  # type: ignore
        order.used_time = None  # type: ignore
        order.refund_time = None  # type: ignore
        order.ap_time = None  # type: ignore

        # 3. 根据状态和新的创建时间设置支付、使用、退款时间
        payment_base_time = None

        if order.status in [OrderStatus.paid, OrderStatus.used, OrderStatus.refunding, OrderStatus.refunded]:
            payment_delay = datetime.timedelta(minutes=random.randint(1, 20))
            order.payment_time = new_created_time + payment_delay
            payment_base_time = order.payment_time

        if order.status == OrderStatus.used and payment_base_time:
            if random.choice([True, False]):
                ap_delay = datetime.timedelta(days=random.randint(1, 3), hours=random.randint(1, 12))
                order.ap_time = payment_base_time + ap_delay
                use_delay = datetime.timedelta(hours=random.randint(1, 8))
                order.used_time = order.ap_time + use_delay
            else:
                use_delay = datetime.timedelta(days=random.randint(1, 5), hours=random.randint(1, 12))
                order.used_time = payment_base_time + use_delay

        if order.status in [OrderStatus.refunding, OrderStatus.refunded] and payment_base_time:
            order.refund_reason = '用户申请退款'
            order.refund_amount = order.amount
            if not order.refund_no:
                order.refund_no = f'test_refund_no_{order.id}_{random.randint(1000, 9999)}'

            if order.status == OrderStatus.refunded:
                refund_delay = datetime.timedelta(days=random.randint(1, 5))
                order.refund_time = payment_base_time + refund_delay
                if not order.wx_refund_id:
                    order.wx_refund_id = f'test_wx_refund_id_{order.id}_{random.randint(1000, 9999)}'

        # 4. 保存所有更改，包括新的创建时间
        await order.save(
            update_fields=[
                'created',
                'payment_time',
                'used_time',
                'refund_time',
                'ap_time',
                'refund_reason',
                'refund_amount',
                'refund_no',
                'wx_refund_id',
            ]
        )

    print(f'成功修正并分散了 {len(orders)} 个订单的时间戳。')


async def main():
    """主函数"""
    await Tortoise.init(config=DATABASE)
    await fix_timestamps_and_distribute()
    await Tortoise.close_connections()
    print('\n所有订单时间戳修正和分散完成！')


if __name__ == '__main__':
    run_async(main())
