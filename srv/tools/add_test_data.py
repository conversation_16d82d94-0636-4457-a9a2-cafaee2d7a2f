import datetime
import random
from itertools import cycle

from tortoise import <PERSON><PERSON><PERSON>, run_async

from apps.exhi.models import Joint, Ticket
from apps.exhi.schemas import TicketCategory as Catg
from apps.order.models import Order
from apps.order.schemas import OrderStatus
from apps.user.models import User
from config.server import DATABASE, TZ

# 配置
USER_COUNT = 20  # 确保数据库中至少有20个用户


async def add_test_users() -> None:
    """添加测试用户，确保总数达到 USER_COUNT"""
    print('正在检查并创建测试用户...')
    existing_user_count = await User.all().count()
    needed = USER_COUNT - existing_user_count

    if needed <= 0:
        print(f'数据库中已有 {existing_user_count} 个用户，无需创建新用户。')
        return

    print(f'需要创建 {needed} 个新用户。')
    users_to_create = []
    for i in range(needed):
        while True:
            unique_id = f'test_openid_{random.randint(100000, 999999)}'
            if not await User.exists(openid=unique_id):
                break
        users_to_create.append(
            User(
                name=f'测试用户-{existing_user_count + i}',
                avatar='https://example.com/avatar.png',
                openid=unique_id,
                phone=f'187{random.randint(10000000, 99999999)}',
            )
        )
    if users_to_create:
        await User.bulk_create(users_to_create)
        print(f'成功创建 {len(users_to_create)} 个测试用户。')


async def add_realistic_orders() -> None:  # noqa: C901
    """在过去三个月内，每天创建符合真实分布的订单"""
    print('开始创建更真实的测试订单...')
    users = await User.all()
    if not users:
        print('错误：数据库中没有用户，请先添加用户。')
        return

    tickets = await Ticket.all()
    joints = await Joint.all()
    all_tickets: list[Ticket | Joint] = tickets + joints

    if not all_tickets:
        print('错误：数据库中没有票据，请先添加票据。')
        return

    # 定义订单状态及其权重，"已使用" 权重最高
    status_choices = [
        OrderStatus.used,
        OrderStatus.paid,
        OrderStatus.refunded,
        OrderStatus.canceled,
        OrderStatus.pending,
        OrderStatus.closed,
        OrderStatus.failed,
        OrderStatus.refunding,
    ]
    status_weights = [50, 15, 10, 10, 5, 5, 3, 2]

    now_aware = datetime.datetime.now(TZ)
    user_cycle = cycle(users)
    total_created = 0

    # 从过去第90天开始，循环到今天
    for day_offset in range(90, -1, -1):
        current_day_start = (now_aware - datetime.timedelta(days=day_offset)).replace(
            hour=0, minute=0, second=0, microsecond=0
        )
        orders_for_the_day = random.randint(10, 30)
        print(f'--- 为 {current_day_start.strftime("%Y-%m-%d")} 创建 {orders_for_the_day} 个订单 ---')

        for _ in range(orders_for_the_day):
            # 1. 选择状态和创建时间
            status = random.choices(status_choices, weights=status_weights, k=1)[0]
            random_seconds_in_day = random.randint(0, 86399)
            created_time = current_day_start + datetime.timedelta(seconds=random_seconds_in_day)

            # 如果创建时间在未来（主要针对 day_offset=0 的情况），则跳过
            if created_time > now_aware:
                continue

            # 2. 准备订单基础数据
            user = next(user_cycle)
            ticket: Ticket | Joint = random.choice(all_tickets)
            await ticket.prefetch()

            if not ticket.prices:
                continue
            timeslot = random.choice(list(ticket.prices.keys()))
            if not ticket.prices[timeslot]:
                continue

            grade_info = random.choice(ticket.prices[timeslot])
            grade, price = grade_info[0], grade_info[1]
            quantity = random.randint(1, 2)
            amount = price * quantity

            sub_tids = []
            if ticket.catg == Catg.joint:
                await ticket.prefetch()
                if ticket.tickets and len(ticket.tickets) >= ticket.n_unit:  # type: ignore
                    sub_tids = [t.id for t in random.sample(ticket.tickets, ticket.n_unit)]  # type: ignore

            archive_attrs = await ticket.archive(timeslot, grade, sub_tids)
            archive = [list(item) for item in archive_attrs]

            base_order_data = {
                'uid': user.id,
                'tid': ticket.id,
                'vid': ticket.vid,
                'phone': user.phone,
                'catg': ticket.catg,
                'quantity': quantity,
                'amount': amount,
                'timeslot': timeslot,
                'grade': grade,
                'archive': archive,
                'sub_tids': sub_tids,
            }

            # 3. 创建并修正订单
            try:
                order = await Order.create(**base_order_data)
                order.created = created_time
                order.status = status
                payment_base_time = None

                # 衍生其他时间戳，并确保它们不超过当前时间
                if status in [OrderStatus.paid, OrderStatus.used, OrderStatus.refunding, OrderStatus.refunded]:
                    payment_time = created_time + datetime.timedelta(minutes=random.randint(1, 20))
                    if payment_time < now_aware:
                        order.payment_time = payment_time
                        payment_base_time = payment_time
                        order.wx_transaction_id = f'test_wx_id_{order.id}_{random.randint(1000, 9999)}'
                        order.vcode = str(random.randint(1000000000, 9999999999))
                    else:  # 如果支付时间在未来，则将状态降级为 pending
                        order.status = OrderStatus.pending

                if order.status == OrderStatus.used and payment_base_time:
                    use_delay_days = (
                        random.randint(0, (now_aware - payment_base_time).days)
                        if (now_aware - payment_base_time).days > 0
                        else 0
                    )
                    used_time = payment_base_time + datetime.timedelta(
                        days=use_delay_days, seconds=random.randint(0, 80000)
                    )
                    if used_time < now_aware:
                        order.used_time = used_time
                    else:  # 如果核销时间在未来，则降级为 paid
                        order.status = OrderStatus.paid

                if order.status in [OrderStatus.refunding, OrderStatus.refunded] and payment_base_time:
                    order.refund_amount = amount
                    order.refund_reason = '用户申请退款'
                    order.refund_no = f'test_refund_no_{order.id}_{random.randint(1000, 9999)}'

                    if order.status == OrderStatus.refunded:
                        refund_time = payment_base_time + datetime.timedelta(days=random.randint(1, 5))
                        if refund_time < now_aware:
                            order.refund_time = refund_time
                            order.wx_refund_id = f'test_wx_refund_id_{order.id}_{random.randint(1000, 9999)}'
                        else:  # 如果退款时间在未来，则降级为 refunding
                            order.status = OrderStatus.refunding

                await order.save()
                total_created += 1
            except Exception as e:
                print(f'创建订单失败: {e}')
    print(f'\n成功创建了 {total_created} 个新订单。')


async def main() -> None:
    """主函数"""
    await Tortoise.init(config=DATABASE)
    await add_test_users()
    await add_realistic_orders()
    await Tortoise.close_connections()
    print('\n测试数据添加完成！')


if __name__ == '__main__':
    run_async(main())
