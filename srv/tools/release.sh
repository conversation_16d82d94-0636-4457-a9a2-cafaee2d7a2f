#!/bin/bash
LOCAL_DIR=$(readlink -f $(dirname $(dirname "$0")))
SERVER=ant
REMOTE_DIR=/opt/hxmp
DELETE=""
RELOAD=false

function show_help() {
    echo "Usage: release.sh [-h] [-D] [-r] [-t]"
    echo "  -h    显示此帮助信息"
    echo "  -D    删除目标目录中多余的文件"
    echo "  -r    同步文件后重启远程服务器"
    echo "  -t    发布到测试服务器"
}

while getopts ":hDrt" opt; do
    case $opt in
        D)
            DELETE="--delete"
            ;;
        r)
            RELOAD=true
            ;;
        t)
            REMOTE_DIR=/opt/hxtest
            ;;
        h)
            show_help
            exit 0
            ;;
        \?)
            echo "无效的参数: -$OPTARG" >&2
            show_help
            exit 1
            ;;
    esac
done
shift $((OPTIND-1))

echo "Syncing Files to $SERVER:$REMOTE_DIR"
rsync -acvzHP $DELETE \
      --exclude={.git,.venv,.DS_Store,__pycache__,.vscode,.mypy_cache,.gitignore,upload,logs} \
      $LOCAL_DIR/ $SERVER:$REMOTE_DIR/

if [ "$RELOAD" = true ]; then
    ssh $SERVER "
        printf '\nReloading Server at $REMOTE_DIR\n' &&
        kill -HUP \$(cat $REMOTE_DIR/logs/hxmp.pid)
    "
fi
echo -e "\nRelease Done"
