#!/usr/bin/env python
import sys
from pathlib import Path

from tortoise import run_async

BASE_DIR = Path(__file__).parent.parent.resolve().as_posix()
sys.path.append(BASE_DIR)

from apps.exhi.models import Exhibition, Joint, Ticket
from apps.exhi.schemas import TicketCategory as Catg
from apps.order.models import Order
from libs.attrdict import AttrDict
from libs.orm import init_db


async def fill_order_eids():
    """根据历史数据填充 eids"""
    for o in await Order.all():
        if not o.eids:
            print(f'filling eids for order {o.id}')
            try:
                if o.catg == Catg.joint:
                    o.eids = (await Joint.get(id=o.tid)).eids
                else:
                    o.eids = [(await Ticket.get(id=o.tid)).eid]
            except Exception:
                a = AttrDict(o.archive)
                names = a.themes or [a.theme]
                o.eids = [e.id for e in await Exhibition.filter(name__in=names)]
            await o.save()


async def main():
    await init_db()

    print('开始修复 Order 的 eids...')
    await fill_order_eids()


if __name__ == '__main__':
    run_async(main())
