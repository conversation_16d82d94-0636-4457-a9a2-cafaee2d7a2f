import time

from fastapi import APIRouter, HTTPException, Request, Response
from fastapi.responses import JSONResponse
from fastapi.templating import Jinja2Templates
from tortoise.exceptions import DoesNotExist

import config as cfg
from libs.http import abort
from libs.http import render as render_json
from libs.state import State
from libs.utils import cn_date, iso_date

router = APIRouter()
templates = Jinja2Templates(directory='admin/templates')

__all__ = [
    'abort',
    'http_exception_handler',
    'not_found_handler',
    'render_html',
    'render_json',
    'router',
]

BREADCRUMBS = {
    # 一级
    'adm': '首页',
    # 二级
    'order': '订单',
    'reservation': '预约',
    'user': '用户',
    'settings': '设置',
    'exhibition': '主题',
    'venue': '展馆',
    'ticket': '门票',
    'joint': '联票',
    'manager': '人员',
    # 三级
    'form': '创建/修改',
    'detail': '详情',
    'save': '保存',
    'delete': '删除',
}


def breadcrumbs(request: Request, context: dict) -> list[dict[str, str]]:
    """生成面包屑导航数据"""
    path = request.url.path
    breadcrumbs = []
    if path.startswith('/adm'):
        parts = filter(lambda p: p, path.strip('/').split('/'))
        parent_path = '/'
        for part in parts:
            if part not in BREADCRUMBS:
                break
            parent_path = f'{parent_path}{part}/'
            if part == 'form':
                name = '修改' if context.get('is_edit') else '创建'
            else:
                name = BREADCRUMBS[part]
            breadcrumbs.append({'name': name, 'url': parent_path})
    return breadcrumbs


def render_html(template_name: str, context: dict | None = None, status_code: int = 200) -> Response:
    """渲染模板的辅助函数"""
    if context is None:
        context = {}
    request: Request = State.get('request')
    context['request'] = request
    context['adm'] = State.get('user')
    context['breadcrumbs'] = breadcrumbs(request, context)
    context['cfg'] = cfg
    context['cn_date'] = cn_date
    context['iso_date'] = iso_date
    context['ver'] = int(time.time())
    return templates.TemplateResponse(template_name, context, status_code=status_code)


async def not_exist_handler(request: Request, exc: DoesNotExist):
    """全局异常处理"""
    if request.url.path.startswith('/adm') and 'text/html' in request.headers.get('accept', ''):
        return render_html(
            'error.html',
            {'status_code': 404, 'detail': '您所查找的对象不存在, 或已被删除', 'title': 'Not Exist'},
            status_code=404,
        )
    else:
        return JSONResponse(status_code=404, content={'detail': '您所查找的对象不存在, 或已被删除'})


async def http_exception_handler(request: Request, exc: HTTPException):
    """根据请求类型返回 HTML 或 JSON 错误"""
    if request.url.path.startswith('/adm') and 'text/html' in request.headers.get('accept', ''):
        if isinstance(exc.detail, str):
            detail = exc.detail
        elif isinstance(exc.detail, dict):
            detail = exc.detail.get('msg') or exc.detail.get('message') or exc.detail
        elif isinstance(exc.detail, list) and isinstance(exc.detail[0], dict):
            detail = exc.detail[0].get('msg') or exc.detail[0].get('message') or exc.detail
        else:
            detail = exc.detail

        return render_html(
            'error.html',
            {'status_code': exc.status_code, 'detail': detail, 'title': type(exc).__name__},
            status_code=exc.status_code,
        )
    else:
        return JSONResponse(status_code=exc.status_code, content={'detail': exc.detail}, headers=exc.headers)


async def not_found_handler(request: Request, exc: HTTPException):
    """处理 404 错误"""
    if request.url.path.startswith('/adm') and 'text/html' in request.headers.get('accept', ''):
        return render_html(
            'error.html',
            {'status_code': 404, 'detail': '您访问的页面不存在', 'title': 'Not Found'},
            status_code=404,
        )
    else:
        return JSONResponse(status_code=404, content={'detail': exc.detail})
