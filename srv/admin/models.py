from enum import StrEnum
from functools import cached_property
from typing import Any, ClassVar, Self

from starlette.authentication import BaseUser
from tortoise import fields

from apps.exhi.models import Venue
from config import SECRET_KEY, SETTINGS
from libs.attrdict import AttrDict
from libs.orm import Model, relate_cache
from libs.utils import make_signature, verify_signature


class Role(StrEnum):
    """管理员角色枚举"""

    SUPER = '超级管理员'
    ADMIN = '普通管理员'
    MANAGER = '店长'
    STAFF = '员工'
    OPERATOR = '运营'


class Admin(Model, BaseUser):
    """管理员用户表"""

    username = fields.CharField(max_length=32, unique=True, description='用户名')
    password = fields.CharField(max_length=128, null=False, description='密码')
    phone = fields.CharField(max_length=11, null=False, description='手机号')
    intro = fields.Char<PERSON>ield(max_length=128, null=True, description='简介')

    # 权限相关字段
    role = fields.CharEnumField(Role, max_length=16, default=Role.STAFF, null=False, description='角色')
    vid = fields.IntField(null=True, description='关联门店ID（店长和员工必填）')

    created = fields.DatetimeField(auto_now_add=True, description='创建时间')
    updated = fields.DatetimeField(auto_now=True, description='更新时间')

    venue: Venue | None = None

    class Meta:  # type: ignore
        table = 'admin_user'
        ordering: ClassVar[list[str]] = ['id']

    @property
    def is_authenticated(self) -> bool:
        return True

    @property
    def is_adm(self) -> bool:
        return True

    @property
    def display_name(self) -> str:
        return self.username

    @staticmethod
    def make_password(plain_password: str):
        return make_signature(str(plain_password), secret_key=SECRET_KEY)

    def verify_password(self, plain_password: str):
        """验证密码"""
        return verify_signature(str(plain_password), self.password, secret_key=SECRET_KEY)

    @classmethod
    async def check_phone(cls, phone: str):
        """根据手机号开启用户管理权限"""
        adm = await cls.get_or_none(phone=phone)
        return bool(adm and adm.can_checkin_orders)

    async def set_adm_user(self):
        """检查同手机号用户的管理员权限"""
        from apps.user.models import User

        if user := await User.get_or_none(phone=self.phone):
            if user.is_adm != self.can_checkin_orders:
                user.is_adm = self.can_checkin_orders
                await user.save()

    async def unset_adm_user(self):
        """根据手机号撤销用户管理权限"""
        from apps.user.models import User

        if user := await User.get_or_none(phone=self.phone):
            if user.is_adm is not False:
                user.is_adm = False
                await user.save()

    @relate_cache
    async def load_venue(self):
        """加载关联的门店信息"""
        if self.vid:
            return await Venue.get(id=self.vid)
        return None

    async def belonged(self) -> str:
        """所属门店名称"""
        if self.role in [Role.MANAGER, Role.STAFF]:
            venue = await self.load_venue()  # type: ignore
            return venue.name if venue else '未知'
        return '蓝色宇宙'

    @cached_property
    def can_create_roles(self) -> list[Role]:
        """列举当前可以创建的角色"""
        return {
            Role.SUPER: [Role.SUPER, Role.ADMIN, Role.MANAGER, Role.STAFF, Role.OPERATOR],
            Role.ADMIN: [Role.MANAGER, Role.STAFF, Role.OPERATOR],
            Role.MANAGER: [Role.STAFF],
            Role.STAFF: [],
            Role.OPERATOR: [],
        }[self.role]

    def can_create_role(self, role: Role) -> bool:
        """检查是否可以创建指定角色的账号"""
        return role in self.can_create_roles

    def can_edit_adm(self, adm: Self) -> bool:
        """检查是否可以编辑指定的管理员"""
        if self.id == adm.id:
            return True
        elif self.role == Role.SUPER:
            return True
        elif self.role == Role.ADMIN:
            return adm.role in [Role.MANAGER, Role.STAFF, Role.OPERATOR]
        elif self.role == Role.MANAGER:
            return adm.role == Role.STAFF and adm.vid == self.vid
        else:
            return False

    def can_delete_adm(self, adm: Self) -> bool:
        """检查是否可以删除指定的管理员"""
        if self.id == adm.id:
            return False  # 不能删除自己
        elif self.role == Role.SUPER:
            return True
        elif self.role == Role.ADMIN:
            return adm.role in [Role.MANAGER, Role.STAFF, Role.OPERATOR]
        elif self.role == Role.MANAGER:
            return adm.role == Role.STAFF and adm.vid == self.vid
        else:
            return False

    @cached_property
    def can_view_users(self) -> bool:
        """是否可以查看用户"""
        return self.role in [
            Role.SUPER,
            Role.ADMIN,
            Role.MANAGER,
            Role.STAFF,
            Role.OPERATOR,
        ]

    @cached_property
    def can_edit_users(self) -> bool:
        """是否可以编辑用户"""
        return self.role in [Role.SUPER, Role.ADMIN, Role.MANAGER]

    @cached_property
    def can_delete_users(self) -> bool:
        """是否可以删除用户"""
        return self.role == Role.SUPER

    @cached_property
    def can_create_orders(self) -> bool:
        """是否可以创建订单"""
        return self.role == Role.SUPER

    @cached_property
    def can_edit_orders(self) -> bool:
        """是否可以编辑订单"""
        return self.role == Role.SUPER

    @cached_property
    def can_checkin_orders(self) -> bool:
        """是否可以核销订单"""
        return self.role in [Role.SUPER, Role.ADMIN, Role.MANAGER, Role.STAFF]

    @cached_property
    def can_delete_orders(self) -> bool:
        """是否可以删除订单"""
        return self.role == Role.SUPER

    @cached_property
    def can_manage_exhibitions(self) -> bool:
        """是否可以管理展览"""
        return self.role in [Role.SUPER, Role.ADMIN]

    def can_manage_venue(self, vid: int) -> bool:
        """检查是否可以管理指定门店"""
        if self.role in [Role.SUPER, Role.ADMIN]:
            return True
        else:
            return self.vid == vid

    @cached_property
    def can_manage_venues(self) -> bool:
        """是否可以管理展馆"""
        return self.role in [Role.SUPER, Role.ADMIN]

    @cached_property
    def can_manage_tickets(self) -> bool:
        """是否可以管理门票"""
        return self.role in [Role.SUPER, Role.ADMIN]

    @cached_property
    def can_manage_admins(self) -> bool:
        """是否可以管理管理员"""
        return self.role in [Role.SUPER, Role.ADMIN, Role.MANAGER]

    @cached_property
    def can_manage_settings(self) -> bool:
        """是否可以管理系统设置"""
        return self.role in [Role.SUPER, Role.ADMIN]

    @cached_property
    def can_view_all_data(self) -> bool:
        """是否可以查看所有数据"""
        return self.role in [Role.SUPER, Role.ADMIN, Role.OPERATOR]


class Setting(Model):
    """系统设置表"""

    class Type(StrEnum):
        INT = 'int'
        FLOAT = 'float'
        BOOL = 'bool'
        STR = 'str'
        TEXT = 'text'
        JSON = 'json'

    name = fields.CharField(max_length=32, unique=True, description='配置项名称')
    vtype = fields.CharEnumField(Type, max_length=8, null=False, description='配置项类型')
    vint = fields.IntField(null=True, description='整型值')
    vfloat = fields.FloatField(null=True, description='浮点型值')
    vstr = fields.CharField(max_length=256, null=True, description='字符串值')
    vtext = fields.TextField(null=True, description='文本值')
    vjson: Any = fields.JSONField(null=True, description='JSON值')

    created = fields.DatetimeField(auto_now_add=True, description='创建时间')
    updated = fields.DatetimeField(auto_now=True, description='更新时间')

    @property
    def value(self):
        match self.vtype:
            case self.Type.INT:
                return self.vint
            case self.Type.FLOAT:
                return self.vfloat
            case self.Type.BOOL:
                return bool(self.vint)
            case self.Type.STR:
                return self.vstr
            case self.Type.TEXT:
                return self.vtext
            case self.Type.JSON:
                return self.vjson

        raise TypeError(f'Invalid setting type: {self.vtype}')

    @classmethod
    async def init_from_cfg(cls):
        """根据配置的 Key 进行初始化"""
        for name, vtype in SETTINGS.items():
            await cls.get_or_create(name=name, defaults={'vtype': vtype})

    @classmethod
    async def value_of(cls, name):
        if obj := await cls.get_or_none(name=name):
            return obj.value
        return None

    @classmethod
    async def values(cls, *keys) -> AttrDict:
        if keys:
            return AttrDict({o.name: o.value for o in await cls.filter(name__in=keys)})
        else:
            return AttrDict({o.name: o.value for o in await cls.all()})

    @classmethod
    async def remove(cls, *names):
        """删除指定的配置项"""
        if not names:
            return
        await cls.filter(name__in=names).delete()
