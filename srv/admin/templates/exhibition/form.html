{% extends "base.html" %}

{% block content %}
  <h2 class="h2">{{ '编辑' if is_edit else '添加' }}展览</h2>

  <form method="post"
        action="/adm/exhibition/save/"
        enctype="multipart/form-data">
    {% if is_edit %}<input type="hidden" name="eid" value="{{ exhibition.id }}" />{% endif %}

    <div class="mb-3 col-md-6">
      <label for="name" class="form-label">展览名称</label>
      <input type="text"
             class="form-control border-primary-subtle"
             id="name"
             name="name"
             value="{{ exhibition.name if exhibition else '' }}"
             required />
    </div>

    <div class="mb-3 col-md-6">
      <label for="vid" class="form-label">所属展馆</label>
      <select class="form-select border-primary-subtle"
              id="vid"
              name="vid">
        <option value="">选择展馆</option>
        {% for venue in venues %}
          <option value="{{ venue.id }}"
                  {% if exhibition and exhibition.vid == venue.id %}selected{% endif %}>{{ venue.name }}</option>
        {% endfor %}
      </select>
    </div>

    <div class="mb-3 col-md-6">
      <label for="thumbnail" class="form-label">缩略图</label>
      <input type="file"
             class="form-control border-primary-subtle"
             id="thumbnail"
             name="thumbnail"
             accept="image/jpeg"
             {% if not is_edit %}required{% endif %} />
      <div class="form-text">
        文件大小不能超过5MB，仅支持 <span class="text-danger fw-bold">JPG 格式</span>，分辨率 300 x 400 (px)
      </div>
      {% if is_edit and exhibition.thumbnail %}
        <img src="{{ exhibition.thumbnail }}"
             alt="Thumbnail"
             width="100"
             class="mt-2" />
        <input type="hidden"
               name="thumbnail_path"
               value="{{ exhibition.thumbnail }}" />
      {% endif %}
    </div>

    <div class="mb-3 col-md-6">
      <label for="banner" class="form-label">概览图</label>
      <input type="file"
             class="form-control border-primary-subtle"
             id="banner"
             name="banner"
             accept="image/jpeg"
             {% if not is_edit %}required{% endif %} />
      <div class="form-text">
        文件大小不能超过5MB，仅支持 <span class="text-danger fw-bold">JPG 格式</span>
      </div>
      <div class="form-text">宽高比 16 : 10，最小宽度 960px，最大宽度 1280px</div>
      {% if is_edit and exhibition.banner %}
        <img src="{{ exhibition.banner }}"
             alt="Banner"
             width="200"
             class="mt-2" />
        <input type="hidden"
               name="banner_path"
               value="{{ exhibition.banner }}" />
      {% endif %}
    </div>

    <div class="mb-3 col-md-6">
      <label for="details" class="form-label">详情图</label>
      <div id="detail-images-container">
        {% if is_edit and exhibition.details %}
          {% for detail_url in exhibition.details %}
            <div class="existing-detail-image mb-2 d-flex align-items-center">
              <img src="{{ detail_url }}"
                   alt="Detail"
                   height="100"
                   class="me-2" />
              <input type="hidden" name="details_paths" value="{{ detail_url }}" />
              <button type="button"
                      class="btn btn-danger btn-sm"
                      onclick="this.parentElement.remove()">删除</button>
            </div>
          {% endfor %}
        {% endif %}
      </div>
      <div id="new-detail-images">
        <input type="file"
               class="form-control border-primary-subtle mb-2"
               name="details"
               accept="image/jpeg" />
      </div>
      <button type="button"
              class="btn btn-secondary btn-sm"
              onclick="addDetailImage()">+ 添加图片</button>
      <div class="form-text">
        文件大小不能超过5MB，仅支持 <span class="text-danger fw-bold">JPG 格式</span>
      </div>
      <div class="form-text">最小宽度 960px，最大宽度 1280px，高度不限</div>
    </div>

    <script>
      function addDetailImage() {
        const container = document.getElementById('new-detail-images');
        const input = document.createElement('input');
        input.type = 'file';
        input.className = 'form-control border-primary-subtle mb-2';
        input.name = 'details';
        input.accept = 'image/jpeg';
        container.appendChild(input);
      }
    </script>

    <button type="submit" class="btn btn-primary">保存</button>
    <a href="/adm/exhibition/" class="btn btn-secondary">取消</a>
  </form>
{% endblock content %}
