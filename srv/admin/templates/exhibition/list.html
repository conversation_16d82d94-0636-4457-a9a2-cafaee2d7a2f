{% extends "base.html" %}

{% block content %}
  <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h2 class="h2">主题管理</h2>
    {% if adm and adm.can_manage_exhibitions %}
      <div class="btn-toolbar mb-2">
        <a href="/adm/exhibition/form/"
           class="btn btn-sm btn-outline-primary">
          <i class="fas fa-plus"></i>
          新增展览
        </a>
      </div>
    {% endif %}
  </div>

  {% if exhibitions %}
    <div class="table-responsive">
      <table class="table table-striped table-hover table-bordered align-middle table-sm">
        <thead>
          <tr>
            <th class="px-2 text-center w-5">ID</th>
            <th class="px-2 w-25">展览名称</th>
            <th class="px-2">所属展馆</th>
            <th class="px-2 text-center">Banner 图</th>
            {% if adm and adm.can_manage_exhibitions %}<th class="text-center w-15">操作</th>{% endif %}
          </tr>
        </thead>
        <tbody>
          {% for exhibition in exhibitions %}
            <tr>
              <td class="px-2 text-center">{{ exhibition.id }}</td>
              <td class="px-2">
                <a href="/adm/exhibition/detail/?eid={{ exhibition.id }}">{{ exhibition.name }}</a>
              </td>
              <td class="px-2">{{ exhibition.venue.name if exhibition.venue else '蓝色宇宙' }}</td>
              <td class="px-2 text-center">
                <img src="{{ exhibition.banner }}"
                     alt="{{ exhibition.name }}"
                     class="rounded list-banner" />
              </td>
              {% if adm and adm.can_manage_exhibitions %}
                <td class="text-center">
                  <div class="btn-group">
                    <a href="/adm/exhibition/detail/?eid={{ exhibition.id }}"
                       class="btn btn-sm btn-primary">详情</a>
                    <a href="/adm/exhibition/form/?eid={{ exhibition.id }}"
                       class="btn btn-sm btn-warning">修改</a>
                    <a href="/adm/exhibition/delete/?eid={{ exhibition.id }}"
                       class="btn btn-sm btn-danger"
                       onclick="return confirm('确定要删除吗？');">删除</a>
                  </div>
                </td>
              {% endif %}
            </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  {% else %}
    <div class="alert alert-warning" role="alert">
      <i class="fas fa-exclamation-triangle"></i>
      尚未添加任何展览
    </div>
  {% endif %}
{% endblock content %}
