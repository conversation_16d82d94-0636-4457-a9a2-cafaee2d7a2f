{% extends "base.html" %}

{% block content %}
  <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h2 class="h2">
      <span class="text-primary fw-bold">用户详情</span>
    </h2>
    {% if adm and adm.can_edit_users %}
      <div class="btn-toolbar mb-2">
        <a href="/adm/user/form/?user_id={{ user.id }}"
           class="btn btn-sm btn-outline-danger">
          <i class="fas fa-edit"></i>
          修改用户
        </a>
      </div>
    {% endif %}
  </div>

  <div class="row">
    <div class="col-md-12">
      <table class="table table-striped align-middle">
        <tbody>
          <tr>
            <th scope="row" class="w-25 text-start">用户 ID</th>
            <td class="px-2">{{ user.id }}</td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">用户名</th>
            <td class="px-2">{{ user.name }}</td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">手机号</th>
            <td class="px-2 font-monospace">{{ user.phone or '未绑定' }}</td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">微信 OpenID</th>
            <td class="px-2 font-monospace">{{ user.openid }}</td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">管理员权限</th>
            <td class="px-2">
              {% if user.is_adm %}
                <span class="badge bg-success">是</span>
              {% else %}
                <span class="badge bg-secondary">否</span>
              {% endif %}
            </td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">注册时间</th>
            <td class="px-2">{{ cn_date(user.created) }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <div class="row mt-4">
    <div class="col-md-12">
      <h4 class="mb-3">历史订单</h4>
      {% if orders %}
        <div class="table-responsive">
          <table class="table table-striped table-hover table-bordered table-sm align-middle">
            <thead>
              <tr>
                <th class="px-2">商户订单号</th>
                <th class="px-2 text-center">订单状态</th>
                <th class="px-2">所属展馆</th>
                <th class="px-2">场次</th>
                <th class="px-2">票档</th>
                <th class="px-2 text-end">金额 (元)</th>
              </tr>
            </thead>
            <tbody>
              {% for order in orders %}
                <tr>
                  <td class="px-2 font-monospace">
                    <a href="/adm/order/detail/?order_id={{ order.id }}">{{ order.trade_no }}</a>
                  </td>
                  {% if order.status == '待使用' %}
                    <td class="px-2 text-center">
                      <span class="badge text-bg-success px-3">{{ order.status }}</span>
                    </td>
                  {% elif order.status == '待支付' %}
                    <td class="px-2 text-center">
                      <span class="badge text-bg-info px-3">{{ order.status }}</span>
                    </td>
                  {% elif order.status == '已使用' %}
                    <td class="px-2 text-center">
                      <span class="badge text-bg-dark px-3">{{ order.status }}</span>
                    </td>
                  {% else %}
                    <td class="px-2 text-center">
                      <span class="badge text-bg-secondary px-3">{{ order.status }}</span>
                    </td>
                  {% endif %}
                  <td class="px-2">{{ order.venue.name }}</td>
                  <td class="px-2">{{ order.timeslot }}</td>
                  <td class="px-2">{{ order.grade }}</td>
                  <td class="px-2 text-end">{{ order.amount }}</td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      {% else %}
        <div class="alert alert-warning" role="alert">
          <i class="fas fa-exclamation-triangle"></i>
          该用户暂无订单
        </div>
      {% endif %}
    </div>
  </div>
{% endblock content %}
