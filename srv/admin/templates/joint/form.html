{% extends "base.html" %}

{% block content %}
  <h2 class="mb-4">{{ '编辑' if is_edit else '添加' }}联票</h2>

  <form method="post"
        action="/adm/joint/save/"
        enctype="multipart/form-data"
        id="jointForm">
    {% if is_edit %}<input type="hidden" name="jid" value="{{ joint.id }}" />{% endif %}
    <div class="mb-3 col-md-6">
      <label for="title" class="form-label">标题</label>
      <input type="text"
             class="form-control border-primary-subtle"
             id="title"
             name="title"
             value="{{ joint.title if joint else '' }}"
             required />
    </div>
    <div class="mb-3 col-md-6">
      <label for="vid" class="form-label">展馆</label>
      <select class="form-select border-primary-subtle"
              id="vid"
              name="vid"
              required>
        {% for venue in venues %}
          <option value="{{ venue.id }}"
                  {% if is_edit and joint.vid == venue.id %}selected{% endif %}>【{{ venue.city }}】{{ venue.name }}</option>
        {% endfor %}
      </select>
    </div>
    <div class="mb-3 col-md-6">
      <label for="n_unit" class="form-label">
        子票数量<span class="form-text">（几联票, 最小为 2）</span>
      </label>
      <input type="number"
             class="form-control border-primary-subtle"
             id="n_unit"
             name="n_unit"
             value="{{ joint.n_unit if is_edit and joint.n_unit else 2 }}"
             min="2"
             required />
    </div>
    <div class="mb-3 col-md-6">
      <label for="eids" class="form-label">
        可选的主题<span class="form-text">（不能小于“子票数”）</span>
      </label>
      <div id="eids" class="form-control border-primary-subtle">
        <!-- 展览列表将通过JS动态加载 -->
      </div>
    </div>
    <div class="row g-3">
      <div class="mb-3 col-md-3">
        <label for="start" class="form-label">
          联票展期<span class="form-text">（留空代表常驻展）</span>
        </label>
        <div class="input-group">
          <div class="input-group-text">开始日期</div>
          <input type="date"
                 class="form-control border-primary-subtle"
                 id="start"
                 name="start"
                 value="{{ joint.start|string if is_edit and joint.start else '' }}" />
        </div>
      </div>
      <div class="mb-3 col-md-3">
        <label for="end" class="form-label">&nbsp;</label>
        <div class="input-group">
          <div class="input-group-text">结束日期</div>
          <input type="date"
                 class="form-control border-primary-subtle"
                 id="end"
                 name="end"
                 value="{{ joint.end|string if is_edit and joint.end else '' }}" />
        </div>
      </div>
    </div>

    <div class="mb-3 col-md-6">
      <label for="thumbnail" class="form-label">缩略图</label>
      <input type="file"
             class="form-control border-primary-subtle"
             id="thumbnail"
             name="thumbnail"
             accept="image/jpeg"
             {% if not is_edit %}required{% endif %} />
      <div class="form-text">
        文件大小不能超过5MB，仅支持 <span class="text-danger fw-bold">JPG 格式</span>，分辨率 300 x 400 (px)
      </div>
      {% if is_edit and joint.thumbnail %}
        <img src="{{ joint.thumbnail }}"
             alt="Thumbnail"
             width="100"
             class="mt-2" />
        <input type="hidden"
               name="thumbnail_path"
               value="{{ joint.thumbnail }}" />
      {% endif %}
    </div>
    <div class="mb-3 col-md-6">
      <label for="banner" class="form-label">概览图</label>
      <input type="file"
             class="form-control border-primary-subtle"
             id="banner"
             name="banner"
             accept="image/jpeg"
             {% if not is_edit %}required{% endif %} />
      <div class="form-text">
        文件大小不能超过5MB，仅支持 <span class="text-danger fw-bold">JPG 格式</span>
      </div>
      <div class="form-text">宽高比 16 : 10，最小宽度 960px，最大宽度 1280px</div>
      {% if is_edit and joint.banner %}
        <img src="{{ joint.banner }}"
             alt="Banner"
             width="200"
             class="mt-2" />
        <input type="hidden" name="banner_path" value="{{ joint.banner }}" />
      {% endif %}
    </div>

    <!-- 详情图 -->
    <div class="mb-3 col-md-6">
      <label for="details" class="form-label">详情图</label>
      <div id="detail-images-container">
        {% if is_edit and joint.details %}
          {% for detail_url in joint.details %}
            <div class="existing-detail-image mb-2 d-flex align-items-center">
              <img src="{{ detail_url }}"
                   alt="Detail"
                   height="100"
                   class="me-2" />
              <input type="hidden" name="details_paths" value="{{ detail_url }}" />
              <button type="button"
                      class="btn btn-danger btn-sm"
                      onclick="this.parentElement.remove()">删除</button>
            </div>
          {% endfor %}
        {% endif %}
      </div>
      <div id="new-detail-images">
        <input type="file"
               class="form-control border-primary-subtle mb-2"
               name="details"
               accept="image/jpeg" />
      </div>
      <button type="button"
              class="btn btn-secondary btn-sm"
              onclick="addDetailImage()">+ 添加图片</button>
      <div class="form-text">
        文件大小不能超过5MB，仅支持 <span class="text-danger fw-bold">JPG 格式</span>
      </div>
      <div class="form-text">最小宽度 960px，最大宽度 1280px，高度不超过 3000px</div>
    </div>

    <!-- 票价设置区域 -->
    <div class="mb-3 col-md-6">
      <div class="form-label">票价设置</div>
      <div id="priceContainer">
        <!-- 动态生成的票价设置内容 -->
      </div>
      <button type="button"
              class="btn btn-outline-primary btn-sm"
              id="addTimeslotBtn">
        <i class="fas fa-plus"></i> 增加场次
      </button>
    </div>

    <input type="hidden" name="prices" id="pricesInput" />
    <button type="submit" class="btn btn-primary">保存</button>
    <a href="/adm/joint/" class="btn btn-secondary">取消</a>
  </form>
  <script id="joint-selected-themes" type="application/json">{{ joint.eids | tojson | safe if is_edit and joint.eids else '[]' }}</script>
  <script id="joint-prices" type="application/json">{{ joint.prices | tojson | safe if is_edit and joint.prices else '{}' }}</script>
  <script>
    function addDetailImage() {
      const container = document.getElementById('new-detail-images');
      const input = document.createElement('input');
      input.type = 'file';
      input.className = 'form-control border-primary-subtle mb-2';
      input.name = 'details';
      input.accept = 'image/jpeg';
      container.appendChild(input);
    }

    // 预定义的场次和票档选项
    const TIMESLOTS = ['全通票', '平日票', '早鸟票', '特惠票'];
    const GRADES = ['单人', '双人', '三人', '儿童', '亲子（1大1小）', '家庭（2大2小）', '学生', '军人', '老年人'];

    // 存储当前票价数据
    let priceData = {};

    // 初始化现有数据
    priceData = JSON.parse(document.getElementById('joint-prices').textContent);

    // 生成场次选择器HTML
    function createTimeslotSelect(selectedValue = '') {
      let options = TIMESLOTS.map(
        (slot) => `<option value="${slot}" ${slot === selectedValue ? 'selected' : ''}>${slot}</option>`
      ).join('');
      options += `<option value="custom" ${!TIMESLOTS.includes(selectedValue) && selectedValue ? 'selected' : ''}>自定义</option>`;

      return `
        <select class="form-select form-select-sm timeslot-select" style="width: 258px;">
          <option value="">选择场次</option>
          ${options}
        </select>
      `;
    }

    // 生成票档选择器HTML
    function createGradeSelect(selectedValue = '') {
      let options = GRADES.map(
        (grade) => `<option value="${grade}" ${grade === selectedValue ? 'selected' : ''}>${grade}</option>`
      ).join('');
      options += `<option value="custom" ${!GRADES.includes(selectedValue) && selectedValue ? 'selected' : ''}>自定义</option>`;

      return `
        <select class="form-select form-select-sm grade-select" style="width: 150px;">
          <option value="">选择票档</option>
          ${options}
        </select>
      `;
    }

    // 创建票档行HTML
    function createGradeRow(timeslot, grade = '', price = '', isFirst = false, isLast = false) {
      const isCustomGrade = grade && !GRADES.includes(grade);

      return `
        <div class="grade-row d-flex align-items-center mb-2">
          ${createGradeSelect(grade)}
          <input type="text" class="form-control form-control-sm ms-2 custom-grade-input" placeholder="自定义票档" value="${isCustomGrade ? grade : ''}" style="width: 120px; display: ${isCustomGrade ? 'block' : 'none'};" />
          <input type="number" class="form-control form-control-sm ms-2 price-input" placeholder="价格" value="${price}" step="0.01" min="0" style="width: 100px;" />
          <button type="button" class="btn btn-outline-danger btn-sm ms-2 remove-grade-btn">
            <i class="fas fa-times"></i>
          </button>
          ${
            isLast
            ? `<button type="button" class="btn btn-outline-success btn-sm ms-1 add-grade-btn"><i class="fas fa-plus"></i></button>`
            : ''
          }
        </div>
      `;
    }

    // 创建场次行HTML
    function createTimeslotRow(timeslot = '', grades = []) {
      const isCustomTimeslot = timeslot && !TIMESLOTS.includes(timeslot);
      let gradeRows = '';

      if (grades.length > 0) {
        gradeRows = grades
          .map(([grade, price], index) =>
            createGradeRow(timeslot, grade, price, index === 0, index === grades.length - 1)
          )
          .join('');
      } else {
        gradeRows = createGradeRow(timeslot, '', '', true, true);
      }

      return `
        <div class="timeslot-group border rounded p-3 mb-3">
          <div class="d-flex align-items-center mb-2">
            ${createTimeslotSelect(timeslot)}
            <input type="text" class="form-control form-control-sm ms-2 custom-timeslot-input" placeholder="自定义场次" value="${isCustomTimeslot ? timeslot : ''}" style="width: 120px; display: ${isCustomTimeslot ? 'block' : 'none'};" />
            <button type="button" class="btn btn-outline-danger btn-sm ms-auto remove-timeslot-btn">
              删除场次
            </button>
          </div>
          <div class="grades-container">
            ${gradeRows}
          </div>
        </div>
      `;
    }

    // 渲染所有票价设置
    function renderPriceSettings() {
      const container = document.getElementById('priceContainer');
      let html = '';

      if (Object.keys(priceData).length === 0) {
        html = createTimeslotRow();
      } else {
        for (const [timeslot, grades] of Object.entries(priceData)) {
          html += createTimeslotRow(timeslot, grades);
        }
      }

      container.innerHTML = html;
      bindEvents();
    }

    // 绑定事件
    function bindEvents() {
      // 场次选择变化
      document.querySelectorAll('.timeslot-select').forEach((select) => {
        select.addEventListener('change', function() {
          const customInput = this.parentNode.querySelector('.custom-timeslot-input');
          if (this.value === 'custom') {
            customInput.style.display = 'block';
            customInput.focus();
          } else {
            customInput.style.display = 'none';
            customInput.value = '';
          }
        });
      });

      // 票档选择变化
      document.querySelectorAll('.grade-select').forEach((select) => {
        select.addEventListener('change', function() {
          const customInput = this.parentNode.querySelector('.custom-grade-input');
          if (this.value === 'custom') {
            customInput.style.display = 'block';
            customInput.focus();
          } else {
            customInput.style.display = 'none';
            customInput.value = '';
          }
        });
      });

      // 删除场次
      document.querySelectorAll('.remove-timeslot-btn').forEach((btn) => {
        btn.addEventListener('click', function() {
          this.closest('.timeslot-group').remove();
        });
      });

      // 删除票档
      document.querySelectorAll('.remove-grade-btn').forEach((btn) => {
        btn.addEventListener('click', function() {
          const gradeRow = this.closest('.grade-row');
          const gradesContainer = gradeRow.parentNode;
          const isLast = gradeRow.querySelector('.add-grade-btn') !== null;

          gradeRow.remove();

          // 如果没有票档了，添加一个空的
          if (gradesContainer.children.length === 0) {
            gradesContainer.innerHTML = createGradeRow('', '', '', true, true);
            bindEvents();
          } else if (isLast) {
            // 如果删除的是最后一个票档，需要给新的最后一个票档添加加号按钮
            const newLastRow = gradesContainer.lastElementChild;
            const addBtn = document.createElement('button');
            addBtn.type = 'button';
            addBtn.className = 'btn btn-outline-success btn-sm ms-1 add-grade-btn';
            addBtn.innerHTML = '<i class="fas fa-plus"></i>';
            newLastRow.appendChild(addBtn);
            bindEvents();
          }
        });
      });

      // 添加票档
      document.querySelectorAll('.add-grade-btn').forEach((btn) => {
        btn.addEventListener('click', function () {
          const gradesContainer = this.closest('.timeslot-group').querySelector('.grades-container');

          // 移除当前最后一个票档的加号按钮
          const currentLastRow = this.closest('.grade-row');
          const addBtn = currentLastRow.querySelector('.add-grade-btn');
          if (addBtn) {
            addBtn.remove();
          }

          // 添加新的票档行（作为新的最后一行，带加号按钮）
          const newRow = document.createElement('div');
          newRow.innerHTML = createGradeRow('', '', '', false, true);
          gradesContainer.appendChild(newRow.firstElementChild);
          bindEvents();
        });
      });
    }

    // 添加场次
    document.getElementById('addTimeslotBtn').addEventListener('click', function() {
      const container = document.getElementById('priceContainer');
      const newGroup = document.createElement('div');
      newGroup.innerHTML = createTimeslotRow();
      container.appendChild(newGroup.firstElementChild);
      bindEvents();
    });

    // 表单提交处理
    document.getElementById('jointForm').addEventListener('submit', function(e) {
      e.preventDefault();

      const checkedExhibitions = document.querySelectorAll('input[name="eids"]:checked').length;
      if (checkedExhibitions < 2) {
        alert('请至少选择 2 个展览');
        return;
      }

      const startInput = document.getElementById('start');
      const endInput = document.getElementById('end');
      const start = startInput.value;
      const end = endInput.value;
      const today = new Date().toISOString().split('T')[0];

      // 验证n_unit字段
      const nUnitInput = document.getElementById('n_unit');
      const nUnit = parseInt(nUnitInput.value);
      if (isNaN(nUnit) || nUnit < 2) {
        alert('联票数量必须是不小于 2 的整数');
        return;
      }

      if (start && end && start >= end) {
        alert('结束日期必须大于开始日期');
        return;
      }

      if (end && end < today) {
        if (!confirm('结束日期已过，确定要继续吗？')) {
          return;
        }
      }

      // 收集表单数据到 FormData
      const formData = new FormData(this);

      // 收集票价数据
      const prices = {};
      document.querySelectorAll('.timeslot-group').forEach((group) => {
        const timeslotSelect = group.querySelector('.timeslot-select');
        const customTimeslotInput = group.querySelector('.custom-timeslot-input');

        let timeslot = timeslotSelect.value;
        if (timeslot === 'custom') {
          timeslot = customTimeslotInput.value.trim();
        }

        if (!timeslot) return;

        const grades = [];
        group.querySelectorAll('.grade-row').forEach((row) => {
          const gradeSelect = row.querySelector('.grade-select');
          const customGradeInput = row.querySelector('.custom-grade-input');
          const priceInput = row.querySelector('.price-input');

          let grade = gradeSelect.value;
          if (grade === 'custom') {
            grade = customGradeInput.value.trim();
          }

          const price = parseFloat(priceInput.value);

          if (grade && !isNaN(price) && price >= 0) {
            grades.push([grade, price]);
          }
        });

        if (grades.length > 0) {
          prices[timeslot] = grades;
        }
      });

      document.getElementById('pricesInput').value = JSON.stringify(prices);

      // 提交表单
      this.submit();
    });

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
      renderPriceSettings();

      const venueSelect = document.getElementById('vid');
      const themesContainer = document.getElementById('eids');
      const selectedThemes = JSON.parse(document.getElementById('joint-selected-themes').textContent);

      function loadThemes(vid) {
        if (!vid) {
          themesContainer.innerHTML = '<p class="text-muted">请先选择展馆</p>';
          return;
        }

        fetch(`/adm/joint/themes/?vid=${vid}`)
          .then(response => response.json())
          .then(data => {
            themesContainer.innerHTML = '';
            if (Object.keys(data).length === 0) {
              themesContainer.innerHTML = '<p class="text-muted">该展馆下没有可用的展览</p>';
              return;
            }

            for (const [id, title] of Object.entries(data)) {
              const isChecked = selectedThemes.includes(parseInt(id));
              const div = document.createElement('div');
              div.className = 'form-check';
              div.innerHTML = `
                <input class="form-check-input" type="checkbox" name="eids" value="${id}" id="theme${id}" ${isChecked ? 'checked' : ''}>
                <label class="form-check-label" for="theme${id}">${title}</label>
              `;
              themesContainer.appendChild(div);
            }
          })
          .catch(error => {
            console.error('Error fetching themes:', error);
            themesContainer.innerHTML = '<p class="text-danger">加载展览失败</p>';
          });
      }

      venueSelect.addEventListener('change', function() {
        loadThemes(this.value);
      });

      // 初始加载
      loadThemes(venueSelect.value);
    });
  </script>
{% endblock content %}
