{% extends "base.html" %}

{% block content %}
  <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h2 class="h2">联票管理</h2>
    <div class="btn-toolbar mb-2">
      <a href="/adm/joint/form/"
         class="btn btn-sm btn-outline-primary">
        <i class="fas fa-plus"></i>
        添加联票
      </a>
    </div>
  </div>

  {% if joints %}
    <div class="table-responsive">
      <table class="table table-striped table-hover table-bordered align-middle table-sm">
        <thead>
          <tr>
            <th class="px-2 text-center w-5">ID</th>
            <th class="px-2 w-25">标题</th>
            <th class="px-2">展馆</th>
            <th class="px-2">几联票</th>
            <th class="px-2 text-center">可选的主题</th>
            <th class="px-2 text-center">价格区间</th>
            <th class="text-center w-15">操作</th>
          </tr>
        </thead>
        <tbody>
          {% for joint in joints %}
            <tr>
              <td class="px-2 text-center">{{ joint.id }}</td>
              <td class="px-2 fw-semibold">
                <a href="/adm/joint/detail/?jid={{ joint.id }}">{{ joint.title }}</a>
              </td>
              <td class="px-2">
                <a href="/adm/venue/detail/?vid={{ joint.venue.id }}">{{ joint.venue.name }}</a>
              </td>
              <td class="px-2 text-center">
                <strong>{{ joint.n_unit }}</strong> 联
              </td>
              <td class="px-2 p-0">
                <table class="table table-sm align-middle mb-0">
                  <tbody>
                    {% for theme in joint.exhibitions %}
                      <tr>
                        <td class="w-75 unset-bg">
                          <a href="/adm/exhibition/detail/?eid={{ theme.id }}">{{ theme.name }}</a>
                        </td>
                        <td class="unset-bg">
                          <img src="{{ theme.banner }}"
                               alt="{{ theme.name }}"
                               class="rounded list-banner me-2" />
                        </td>
                      </tr>
                    {% endfor %}
                  </tbody>
                </table>
              </td>
              <td class="px-2 text-center">
                <strong class="font-monospace">¥{{ joint.price_range[0] }}</strong> - <strong class="font-monospace text-danger">¥{{ joint.price_range[1] }}</strong>
              </td>
              <td class="text-center">
                <div class="btn-group">
                  <a href="/adm/joint/detail/?jid={{ joint.id }}"
                     class="btn btn-sm btn-primary">详情</a>
                  <a href="/adm/joint/form/?jid={{ joint.id }}"
                     class="btn btn-sm btn-warning">编辑</a>
                  <a href="/adm/joint/delete/?jid={{ joint.id }}"
                     class="btn btn-sm btn-danger"
                     onclick="return confirm('确定要删除吗？')">删除</a>
                </div>
              </td>
            </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  {% else %}
    <div class="alert alert-warning" role="alert">
      <i class="fas fa-exclamation-triangle"></i>
      尚未添加任何联票
    </div>
  {% endif %}
{% endblock content %}
