{% extends "base.html" %}

{% block content %}
  <div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2 class="h2 mb-0">数据概览</h2>
      {% if cur_adm.can_view_all_data %}
        <div class="col-auto">
          <select class="form-select"
                  id="venueSelect"
                  onchange="filterByVenue()">
            <option value="">全部展馆</option>
            {% for venue in venues %}
              <option value="{{ venue.id }}"
                      {% if selected_vid == venue.id %}selected{% endif %}>{{ venue.name }}</option>
            {% endfor %}
          </select>
        </div>
      {% endif %}
    </div>

    <div class="row">
      <div class="col-6 col-md-3 mb-3">
        <div class="card text-white bg-primary">
          <div class="card-body">
            <h5 class="card-title">用户总数</h5>
            <p class="card-text fs-4">{{ n_user }}</p>
          </div>
        </div>
      </div>
      <div class="col-6 col-md-3 mb-3">
        <div class="card text-white bg-danger">
          <div class="card-body">
            <h5 class="card-title">订单总量</h5>
            <p class="card-text fs-4">{{ n_order }}</p>
          </div>
        </div>
      </div>
      <div class="col-6 col-md-3 mb-3">
        <div class="card text-white bg-info">
          <div class="card-body">
            <h5 class="card-title">昨日收入</h5>
            <p class="card-text fs-4">{{ income_yesterday }}</p>
          </div>
        </div>
      </div>
      <div class="col-6 col-md-3 mb-3">
        <div class="card text-white bg-warning">
          <div class="card-body">
            <h5 class="card-title">本月收入</h5>
            <p class="card-text fs-4">{{ income_this_month }}</p>
          </div>
        </div>
      </div>
    </div>

    <div class="row mt-4">
      {% if not selected_vid %}
        <div class="col-md-6 mb-4">
          <div class="card">
            <div class="card-body">
              <h5 class="card-title">活跃时间段</h5>
              <canvas id="hourlyActivityChart"></canvas>
            </div>
          </div>
        </div>
        <div class="col-md-6 mb-4">
          <div class="card">
            <div class="card-body">
              <h5 class="card-title">各展馆收入对比</h5>
              <div class="text-center" style="height: 300px;">
                <canvas id="venueIncomeChart"></canvas>
              </div>
            </div>
          </div>
        </div>
      {% endif %}
    </div>

    <div class="row">
      <div class="{% if selected_vid %}
                    col-md-12
                  {% else %}
                    col-md-6
                  {% endif %}
                  mb-4">
        <div class="card">
          <div class="card-body">
            <h5 class="card-title">每日收入</h5>
            <canvas id="dailyIncomeChart"></canvas>
          </div>
        </div>
      </div>
      <div class="{% if selected_vid %}
                    col-md-12
                  {% else %}
                    col-md-6
                  {% endif %}
                  mb-4">
        <div class="card">
          <div class="card-body">
            <h5 class="card-title">每周收入</h5>
            <div style="height: 100%;">
              <canvas id="weeklyIncomeChart"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-md-12 mb-4">
        <div class="card">
          <div class="card-body">
            <h5 class="card-title">每月收入</h5>
            <canvas id="monthlyIncomeChart"></canvas>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock content %}

{% block ext_js %}
  <script id="chart-data" type="application/json">{{ chart_data | safe }}</script>
  <script src="/static/js/chart.umd.min.js"></script>
  <script>
    const chartData = JSON.parse(document.getElementById('chart-data').textContent);
    const dopamineColors = [
      '#FF4D4D', '#FFC700', '#00C853', '#2979FF', '#D500F9',
      '#FF6D00', '#6200EA', '#00B8D4', '#FFD600', '#FF3D00'
    ];
    const dopamineColorsRGBA = [
      'rgba(255, 77, 77, 0.2)', 'rgba(255, 199, 0, 0.2)', 'rgba(0, 200, 83, 0.2)', 'rgba(41, 121, 255, 0.2)', 'rgba(213, 0, 249, 0.2)',
      'rgba(255, 109, 0, 0.2)', 'rgba(98, 0, 234, 0.2)', 'rgba(0, 184, 212, 0.2)', 'rgba(255, 214, 0, 0.2)', 'rgba(255, 61, 0, 0.2)'
    ];

    // 展馆筛选功能
    function filterByVenue() {
      const select = document.getElementById('venueSelect');
      const vid = select.value;
      const url = new URL(window.location);
      if (vid) {
        url.searchParams.set('vid', vid);
      } else {
        url.searchParams.delete('vid');
      }
      window.location.href = url.toString();
    }

    // 活跃时间段 (只在全部展馆时显示)
    if (chartData.hourly_activity) {
      new Chart(document.getElementById('hourlyActivityChart'), {
        type: 'line',
        data: {
          labels: chartData.hourly_activity.labels,
          datasets: [
            {
              label: '今天',
              data: chartData.hourly_activity.today,
              borderColor: dopamineColors[3],
              backgroundColor: dopamineColorsRGBA[3],
              fill: true,
              tension: 0.4
            },
            {
              label: '昨天',
              data: chartData.hourly_activity.yesterday,
              borderColor: dopamineColors[1],
              backgroundColor: dopamineColorsRGBA[1],
              fill: false,
              tension: 0.4
            },
            {
              label: '前天',
              data: chartData.hourly_activity.day_before_yesterday,
              borderColor: dopamineColors[2],
              backgroundColor: dopamineColorsRGBA[2],
              fill: false,
              tension: 0.4
            }
          ]
        },
        options: {
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                precision: 0
              }
            }
          }
        }
      });
    }

    // 每日收入趋势
    new Chart(document.getElementById('dailyIncomeChart'), {
      type: 'bar',
      data: {
        labels: chartData.daily_income.labels,
        datasets: [{
          data: chartData.daily_income.data,
          backgroundColor: dopamineColors[0]
        }]
      },
      options: {
        plugins: {
          legend: { display: false },
          tooltip: {
            callbacks: {
              label: (context) => {
                const v = context.parsed && (context.parsed.y ?? context.parsed);
                return v == null ? '' : String(v);
              }
            }
          }
        }
      }
    });

    // 展馆收入对比 (只在全部展馆时显示)
    if (chartData.venue_income) {
      new Chart(document.getElementById('venueIncomeChart'), {
        type: 'pie',
        data: {
          labels: Object.keys(chartData.venue_income),
          datasets: [{
            data: Object.values(chartData.venue_income),
            backgroundColor: dopamineColors
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false
        }
      });
    }

    // 每周收入
    new Chart(document.getElementById('weeklyIncomeChart'), {
      type: 'bar',
      data: {
        labels: chartData.weekly_income.labels,
        datasets: [{
          data: chartData.weekly_income.data,
          backgroundColor: dopamineColors[4]
        }]
      },
      options: {
        plugins: {
          legend: { display: false },
          tooltip: {
            callbacks: {
              label: (context) => {
                const v = context.parsed && (context.parsed.y ?? context.parsed);
                return v == null ? '' : String(v);
              }
            }
          }
        }
      }
    });

    // 每月收入
    new Chart(document.getElementById('monthlyIncomeChart'), {
      type: 'bar',
      data: {
        labels: chartData.monthly_income.labels,
        datasets: [{
          data: chartData.monthly_income.data,
          backgroundColor: dopamineColors[1]
        }]
      },
      options: {
        plugins: {
          legend: { display: false },
          tooltip: {
            callbacks: {
              label: (context) => {
                const v = context.parsed && (context.parsed.y ?? context.parsed);
                return v == null ? '' : String(v);
              }
            }
          }
        }
      }
    });
  </script>
{% endblock ext_js %}
