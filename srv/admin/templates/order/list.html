{% extends "base.html" %}

{% block content %}
  <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h2 class="h2">订单数据</h2>
    {% if adm and adm.can_create_orders %}
      <div class="btn-toolbar mb-2">
        <a href="/adm/order/form/"
           class="btn btn-sm btn-outline-primary">
          <i class="fas fa-plus"></i>
          新增订单
        </a>
      </div>
    {% endif %}
  </div>

  <!-- 搜索表单 -->
  <form method="get" class="row g-3 mb-3">
    <div class="col-auto">
      <input type="text"
             name="q"
             class="form-control"
             placeholder="订单号/手机号"
             value="{{ q or '' }}" />
    </div>
    <div class="col-auto">
      <button type="submit" class="btn btn-primary">搜索</button>
    </div>
    <div class="col-auto ms-auto">
      <div class="row g-3">
        {% if venues %}
          <div class="col-auto">
            <select name="vid"
                    id="vid-filter"
                    class="form-select"
                    onchange="this.form.submit()">
              <option value="">所有展馆</option>
              {% for venue in venues %}
                <option value="{{ venue.id }}"
                        {% if venue.id == current_vid %}selected{% endif %}>{{ venue.name }}</option>
              {% endfor %}
            </select>
          </div>
        {% endif %}
        <div class="col-auto">
          <select name="eid"
                  id="eid-filter"
                  class="form-select"
                  onchange="this.form.submit()">
            <option value="">所有主题</option>
            {% for exhi in exhibitions %}
              <option value="{{ exhi.id }}"
                      {% if exhi.id == current_eid %}selected{% endif %}>{{ exhi.name }}</option>
            {% endfor %}
          </select>
        </div>
        <div class="col-auto">
          <select name="status"
                  id="status-filter"
                  class="form-select"
                  onchange="this.form.submit()">
            <option value="" {% if not current_status %}selected{% endif %}>全部状态</option>
            {% for s in statuses %}
              <option value="{{ s.value }}"
                      {% if s.value == current_status %}selected{% endif %}>{{ s.value }}</option>
            {% endfor %}
          </select>
        </div>
      </div>
    </div>
  </form>

  {% if orders %}
    <div class="table-responsive">
      <table class="table table-striped table-hover table-bordered table-sm align-middle">
        <thead>
          <tr>
            <th class="px-2 text-center">商户订单号</th>
            <th class="px-2 text-center">用户手机号</th>
            <th class="px-2 text-center w-15">下单时间</th>
            <th class="px-2 text-center w-15">核销时间</th>
            <th class="px-2 text-center">订单状态</th>
            <th class="px-2 text-center">展馆</th>
            <th class="px-2 w-15">门票</th>
            <th class="px-2 text-end">金额</th>
            {% if adm %}
              {% if adm.can_delete_orders %}
                <th class="text-center w-15">操作</th>
              {% elif adm.can_checkin_orders %}
                <th class="text-center w-5">操作</th>
              {% endif %}
            {% endif %}
          </tr>
        </thead>
        <tbody>
          {% for order in orders %}
            <tr>
              <td class="px-2 text-center font-monospace">
                <a href="/adm/order/detail/?order_id={{ order.id }}">{{ order.trade_no }}</a>
              </td>
              <td class="px-2 text-center">
                <a href="/adm/user/detail/?user_id={{ order.uid }}">{{ order.phone }}</a>
              </td>
              <td class="px-2 text-center">{{ iso_date(order.created, 'm') }}</td>
              <td class="px-2 text-center">{{ iso_date(order.used_time, 'm') }}</td>
              <td class="px-2 text-center">
                {% if order.status == '待支付' %}
                  <span class="badge text-bg-info px-1">{{ order.status }}</span>
                {% elif order.status == '待使用' %}
                  <span class="badge text-bg-success px-1">{{ order.status }}</span>
                {% elif order.status == '已使用' %}
                  <span class="badge text-bg-warning px-1">{{ order.status }}</span>
                {% elif order.status == '退款中' %}
                  <span class="badge text-bg-danger px-1">{{ order.status }}</span>
                {% else %}
                  <span class="badge text-bg-secondary px-1">{{ order.status }}</span>
                {% endif %}
              </td>
              <td class="px-2 text-center">{{ order.venue.name }}</td>
              <td class="px-2">{{ order.grade + order.timeslot }}</td>
              <td class="px-2 text-end">{{ order.amount|int if order.amount.is_integer() else order.amount }}</td>
              <td class="text-center">
                <div class="btn-group">
                  {% if adm and adm.can_checkin_orders and order.status == '待使用' %}
                    <a href="/adm/order/detail/?order_id={{ order.id }}"
                       class="btn btn-sm btn-primary">核销</a>
                  {% else %}
                    <button type="button"
                            class="btn btn-sm btn-outline-secondary"
                            disabled>核销</button>
                  {% endif %}

                  {% if adm and adm.can_delete_orders and cfg.DEBUG %}
                    <a href="/adm/order/form/?order_id={{ order.id }}"
                       class="btn btn-sm btn-warning">修改</a>
                    <a href="/adm/order/delete/?order_id={{ order.id }}"
                       class="btn btn-sm btn-danger"
                       onclick="return confirm('确定要删除吗？');">删除</a>
                  {% endif %}
                </div>
              </td>
            </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  {% else %}
    <div class="alert alert-warning" role="alert">
      <i class="fas fa-exclamation-triangle"></i>
      暂无订单
    </div>
  {% endif %}

  {% from '_macros.html' import render_pagination with context %}

  <!-- 分页 -->
  {{ render_pagination(page, total_pages, request) }}

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      const selectElement = document.getElementById('status-filter');
      if (!selectElement) return;

      const statusColorMap = {
        '未支付': 'text-bg-secondary',
        '待使用': 'text-bg-success',
        '已使用': 'text-bg-warning',
        '退款': 'text-bg-danger',
      };

      function applyColor() {
        const selectedValue = selectElement.value;
        // Remove all possible color classes first
        Object.values(statusColorMap).forEach(className => {
          selectElement.classList.remove(className);
        });
        // Add the new class if the selected value has a mapping
        if (statusColorMap[selectedValue]) {
          selectElement.classList.add(statusColorMap[selectedValue]);
        }
      }

      // Apply color on page load
      applyColor();
    });
  </script>
{% endblock content %}
