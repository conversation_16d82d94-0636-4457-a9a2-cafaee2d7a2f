{% extends "base.html" %}

{% block content %}
  <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h2 class="h2">预约列表</h2>
  </div>

  <!-- 筛选表单 -->
  <form method="get" class="row g-3 mb-3">
    {% if venues %}
      <div class="col-auto">
        <select name="vid"
                id="vid-filter"
                class="form-select"
                onchange="this.form.submit()">
          <option value="">所有展馆</option>
          {% for venue in venues %}
            <option value="{{ venue.id }}"
                    {% if venue.id == current_vid %}selected{% endif %}>{{ venue.name }}</option>
          {% endfor %}
        </select>
      </div>
    {% endif %}
    <div class="col-auto date-input-wrapper">
      <input type="date"
             name="date"
             class="form-control"
             value="{{ date or '' }}"
             onchange="this.form.submit()" />
    </div>
  </form>

  <style>
    /* 给日期输入框的容器设置相对定位 */
    .col-auto {
      position: relative;
    }

    /* 核心：当 input[type=date] 的值为空时，它不匹配 :valid 伪类 */
    /* 我们可以利用这一点，通过 ::before 伪元素来创建自定义的占位符 */
    input[type="date"]::before {
      content: attr(placeholder);
      position: absolute;
      top: 50%;
      left: 20px;
      transform: translateY(-50%);
      color: #6c757d;
      pointer-events: none;
    }

    /* 当用户选择了日期，输入框变为 :valid 状态，此时隐藏我们的自定义占位符 */
    input[type="date"]:valid::before {
      display: none;
    }
  </style>

  {% if orders %}
    <div class="table-responsive">
      <table class="table table-striped table-hover table-bordered table-sm align-middle">
        <thead>
          <tr>
            <th class="px-2">门票名称</th>
            <th class="px-2">用户手机</th>
            <th class="px-2">预约时间</th>
            <th class="px-2">门票类型</th>
            <th class="px-2 text-center">数量</th>
            {% if adm and adm.can_checkin_orders %}<th class="text-center w-5">操作</th>{% endif %}
          </tr>
        </thead>
        <tbody>
          {% for order in orders %}
            <tr>
              <td class="px-2">
                <a href="/adm/order/detail/?order_id={{ order.id }}">{{ order.ticket.title }}</a>
              </td>
              <td class="px-2">
                <a href="/adm/user/detail/?user_id={{ order.uid }}">{{ order.phone }}</a>
              </td>
              <td class="px-2">{{ cn_date(order.ap_time, 'd') }}</td>
              <td class="px-2">{{ order.grade + order.timeslot }}</td>
              <td class="px-2 text-center">{{ order.quantity }}</td>
              <td class="text-center">
                {% if adm and adm.can_checkin_orders and order.status == '待使用' %}
                  <div class="btn-group">
                    <a href="/adm/order/detail/?order_id={{ order.id }}"
                       class="btn btn-sm btn-primary w-10">核销</a>
                  </div>
                {% endif %}
              </td>
            </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  {% else %}
    <div class="alert alert-warning" role="alert">
      <i class="fas fa-exclamation-triangle"></i>
      暂无预约
    </div>
  {% endif %}

  {% from '_macros.html' import render_pagination with context %}

  <!-- 分页 -->
  {{ render_pagination(page, total_pages, request) }}

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      const dateInput = document.querySelector('input[name="date"]');
      const wrapper = dateInput.parentElement;

      function updatePlaceholder() {
        if (dateInput.value) {
          wrapper.classList.add('has-value');
        } else {
          wrapper.classList.remove('has-value');
        }
      }

      // Initial check
      updatePlaceholder();

      // Update on change
      dateInput.addEventListener('change', updatePlaceholder);
    });
  </script>
{% endblock content %}
