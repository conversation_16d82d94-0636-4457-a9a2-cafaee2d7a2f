{% extends "base.html" %}

{% block content %}
  <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h2 class="h2">
      <span class="text-primary fw-bold">订单详情</span>
    </h2>
    <div class="btn-toolbar mb-2">
      {% if adm and adm.can_edit_orders and cfg.DEBUG %}
        <a href="/adm/order/form/?order_id={{ order.id }}"
           class="btn btn-sm btn-outline-danger">
          <i class="fas fa-edit"></i>
          修改
        </a>
      {% endif %}
      {% if adm and adm.can_checkin_orders and order.status == '待使用' %}
        <button id="checkin-btn" class="btn btn-sm text-bg-warning ms-2">
          <i class="fas fa-check-double fa-beat"></i>
          核销
        </button>
      {% endif %}
    </div>
  </div>

  <div class="row">
    <div class="col-md-12">
      <table class="table table-striped align-middle">
        <tbody>
          <tr>
            <th scope="row" class="w-25 text-start">门票</th>
            <td class="px-2">{{ order.ticket.title }}</td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">IP 主题</th>
            <td class="px-2">
              {% for exhi in order.exhibitions %}
                <a href="/adm/exhibition/detail/?eid={{ exhi.id }}"
                   class="btn btn-sm btn-outline-primary px-1 py-0 me-1">{{ exhi.name }}</a>
              {% endfor %}
            </td>

          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">票档</th>
            <td class="px-2">{{ order.grade }} · {{ order.timeslot }}</td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">数量</th>
            <td class="px-2">{{ order.quantity }}</td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">总金额</th>
            <td class="px-2 font-monospace text-danger fw-bold">¥{{ order.amount }}</td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">所属展馆</th>
            <td class="px-2">
              <a href="/adm/venue/detail/?vid={{ order.venue.id }}">{{ order.venue.name }}</a>
            </td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">门票类型</th>
            <td class="px-2">
              <span class="badge bg-primary">{{ order.catg }}</span>
            </td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">用户手机号</th>
            <td class="px-2">
              <a href="/adm/user/detail/?user_id={{ order.uid }}">{{ order.phone }}</a>
            </td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">订单号</th>
            <td class="px-2 font-monospace">{{ order.trade_no }}</td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">订单状态</th>
            <td class="px-2">
              {% if order.status == '待使用' %}
                <span class="badge text-bg-success">{{ order.status }}</span>
              {% elif order.status == '待支付' %}
                <span class="badge text-bg-info">{{ order.status }}</span>
              {% elif order.status == '已使用' %}
                <span class="badge text-bg-warning">{{ order.status }}</span>
              {% else %}
                <span class="badge text-bg-secondary">{{ order.status }}</span>
              {% endif %}
            </td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">微信支付交易号</th>
            <td class="px-2 font-monospace">{{ order.wx_transaction_id or '' }}</td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">核销码</th>
            <td class="px-2 font-monospace">{{ order.vcode or '' }}</td>
          </tr>
          <tr>
            <th scope="row" class="w-25 text-start">创建时间</th>
            <td class="px-2">{{ cn_date(order.created) }}</td>
          </tr>
          {% if order.payment_time %}
            <tr>
              <th scope="row" class="w-25 text-start">支付时间</th>
              <td class="px-2 text-success">{{ cn_date(order.payment_time) if order.payment_time else '' }}</td>
            </tr>
          {% endif %}
          {% if order.ap_time %}
            <tr>
              <th scope="row" class="w-25 text-start">预约日期</th>
              <td class="px-2 text-primary">{{ cn_date(order.ap_time, 'd') }}</td>
            </tr>
          {% endif %}
          {% if order.used_time %}
            <tr>
              <th scope="row" class="w-25 text-start">核销时间</th>
              <td class="px-2 text-warning">{{ cn_date(order.used_time) }}</td>
            </tr>
          {% endif %}
          {% if order.refund_time %}
            <tr>
              <th scope="row" class="w-25 text-start">退款时间</th>
              <td class="px-2 text-secondary">{{ cn_date(order.refund_time) }}</td>
            </tr>
          {% endif %}
        </tbody>
      </table>
    </div>
  </div>

  <div class="row mt-4">
    <div class="col-md-12">
      <div class="accordion" id="archiveAccordion">
        <div class="accordion-item">
          <h2 class="accordion-header" id="headingOne">
            <button class="accordion-button collapsed"
                    type="button"
                    data-bs-toggle="collapse"
                    data-bs-target="#collapseOne"
                    aria-expanded="false"
                    aria-controls="collapseOne">门票原始信息</button>
          </h2>
          <div id="collapseOne"
               class="accordion-collapse collapse"
               aria-labelledby="headingOne"
               data-bs-parent="#archiveAccordion">
            <div class="accordion-body">
              <table class="table table-bordered table-sm">
                <tbody>
                  {% for key, value in order.archive %}
                    <tr>
                      <th class="w-25">{{ key }}</th>
                      <td>
                        {% if value is mapping %}
                          <pre class="mb-0"><code>{{ value|tojson(indent=2) }}</code></pre>
                        {% elif value is iterable and value is not string %}
                          <ul class="list-unstyled mb-0">
                            {% for item in value %}<li>{{ item }}</li>{% endfor %}
                          </ul>
                        {% else %}
                          {{ value }}
                        {% endif %}
                      </td>
                    </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script id="order-meta" type="application/json">{{ {'vcode': order.vcode} | tojson | safe }}</script>
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      const checkinBtn = document.getElementById('checkin-btn');
      if (checkinBtn) {
        checkinBtn.addEventListener('click', function () {
          if (confirm('门票信息是否要确认无误?')) {
            fetch('/api/order/checkin', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(JSON.parse(document.getElementById('order-meta').textContent)),
            })
              .then(response => response.json())
              .then(data => {
                if (data.msg === '核销成功') {
                  alert('核销成功');
                  location.reload();
                } else {
                  alert('核销失败: ' + (data.detail || '未知错误'));
                }
              })
              .catch(error => {
                console.error('Error:', error);
                alert('核销请求失败');
              });
          }
        });
      }
    });
  </script>
{% endblock content %}
