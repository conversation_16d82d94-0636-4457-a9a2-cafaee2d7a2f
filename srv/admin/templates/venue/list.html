{% extends "base.html" %}

{% block content %}
  <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h2 class="h2">展馆管理</h2>
    {% if adm and adm.can_manage_venues %}
      <div class="btn-toolbar mb-2">
        <a href="/adm/venue/form/"
           class="btn btn-sm btn-outline-primary">
          <i class="fas fa-plus"></i>
          新增展馆
        </a>
      </div>
    {% endif %}
  </div>

  {% if venues %}
    <div class="table-responsive">
      <table class="table table-striped table-hover table-bordered align-middle table-sm">
        <thead>
          <tr>
            <th class="px-2 text-center w-5">ID</th>
            <th class="px-2 w-10">展馆名称</th>
            <th class="px-2 w-10">省份</th>
            <th class="px-2 w-10">城市</th>
            <th class="px-2">地址</th>
            {% if adm and adm.can_manage_venues %}<th class="text-center w-15">操作</th>{% endif %}
          </tr>
        </thead>
        <tbody>
          {% for venue in venues %}
            <tr>
              <td class="px-2 text-center">{{ venue.id }}</td>
              <td class="px-2">
                <a href="/adm/venue/detail/?vid={{ venue.id }}">{{ venue.name }}</a>
              </td>
              <td class="px-2">{{ venue.province }}</td>
              <td class="px-2">{{ venue.city }}</td>
              <td class="px-2">{{ venue.addr }}</td>
              {% if adm and adm.can_manage_venues %}
                <td class="text-center">
                  <div class="btn-group">
                    <a href="/adm/venue/detail/?vid={{ venue.id }}"
                       class="btn btn-sm btn-primary">详情</a>
                    <a href="/adm/venue/form/?vid={{ venue.id }}"
                       class="btn btn-sm btn-warning">修改</a>
                    <a href="/adm/venue/delete/?vid={{ venue.id }}"
                       class="btn btn-sm btn-danger"
                       onclick="return confirm('确定要删除吗？');">删除</a>
                  </div>
                </td>
              {% endif %}
            </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  {% else %}
    <div class="alert alert-warning" role="alert">
      <i class="fas fa-exclamation-triangle"></i>
      尚未添加任何展馆信息
    </div>
  {% endif %}
{% endblock content %}
