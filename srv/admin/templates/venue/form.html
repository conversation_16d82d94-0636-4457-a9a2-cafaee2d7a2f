{% extends "base.html" %}

{% block content %}
  <h2>展馆管理</h2>

  <form method="post" action="/adm/venue/save/">
    {% if venue %}<input type="hidden" name="vid" value="{{ venue.id }}" />{% endif %}
    <div class="mb-3 col-md-6">
      <label for="name" class="form-label">展馆名称</label>
      <input type="text"
             class="form-control border-primary-subtle"
             id="name"
             name="name"
             value="{{ venue.name if venue else '' }}"
             required />
    </div>
    <div class="row">
      <div class="mb-3 col-md-3">
        <label for="province" class="form-label">省份</label>
        <select class="form-select border-primary-subtle"
                id="province"
                name="province"
                required>
          <option value="">选择省份</option>
          {% for p_name in cities %}
            <option value="{{ p_name }}"
                    {% if venue and venue.province == p_name %}selected{% endif %}>{{ p_name }}</option>
          {% endfor %}
        </select>
      </div>
      <div class="mb-3 col-md-3">
        <label for="city" class="form-label">城市</label>
        <select class="form-select border-primary-subtle"
                id="city"
                name="city"
                required>
          <option value="">选择城市</option>
        </select>
      </div>
    </div>
    <div class="mb-3 col-md-6">
      <label for="addr" class="form-label">详细地址</label>
      <input type="text"
             class="form-control border-primary-subtle"
             id="addr"
             name="addr"
             value="{{ venue.addr if venue else '' }}"
             required />
    </div>
    <div class="mb-3 col-md-6">
      <label for="appid" class="form-label">门店 AppID</label>
      <input type="text"
             class="form-control border-primary-subtle"
             id="appid"
             name="appid"
             value="{{ venue.appid if venue and venue.appid else '' }}" />
    </div>
    <div class="mb-3 col-md-6">
      <label for="mchid" class="form-label">门店商户号</label>
      <input type="text"
             class="form-control border-primary-subtle"
             id="mchid"
             name="mchid"
             value="{{ venue.mchid if venue and venue.mchid else '' }}" />
    </div>
    <div class="mb-3">
      <label for="notice" class="form-label">入馆公告</label>
      <textarea class="form-control border-primary-subtle"
                id="notice"
                name="notice"
                rows="5">{{ venue.notice if venue else '' }}</textarea>
    </div>
    <button type="submit" class="btn btn-primary">保存</button>
    <a href="/adm/venue/" class="btn btn-secondary">取消</a>
  </form>
  <div id="exhi-initials"
       data-province="{{ venue.province if venue else '' }}"
       data-city="{{ venue.city if venue else '' }}"></div>
  <script id="cities-data" type="application/json">{{ cities_json | safe }}</script>
  <script>
    document.addEventListener('DOMContentLoaded', function () {
        const citiesData = JSON.parse(document.getElementById('cities-data').textContent);
        const provinceSelect = document.getElementById('province');
        const citySelect = document.getElementById('city');
        const { province: initialProvince, city: initialCity } = document.getElementById('exhi-initials').dataset;

        function populateCities(province) {
            citySelect.innerHTML = '<option value="">选择城市</option>';
            if (province && citiesData[province]) {
                for (const city in citiesData[province]) {
                    const option = document.createElement('option');
                    option.value = city;
                    option.textContent = city;
                    citySelect.appendChild(option);
                }
            }
        }

        provinceSelect.addEventListener('change', function () {
            populateCities(this.value);
        });

        if (initialProvince) {
            populateCities(initialProvince);
            if (initialCity) {
                citySelect.value = initialCity;
            }
        }
    });
  </script>
{% endblock content %}
