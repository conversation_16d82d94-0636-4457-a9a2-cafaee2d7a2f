{% macro render_pagination(page, total_pages, request) %}
  <nav aria-label="Page navigation">
    <ul class="pagination justify-content-center">
      {# 首页按钮 #}
      <li class="page-item {% if page <= 1 %}disabled{% endif %}">
        <a class="page-link"
           href="?{{ dict(request.query_params, page=1) |urlencode }}"><i class="fa-solid fa-backward-fast"></i></a>
      </li>

      {# 上一页按钮 #}
      <li class="page-item {% if page <= 1 %}disabled{% endif %}">
        <a class="page-link"
           href="?{{ dict(request.query_params, page=page-1) |urlencode }}"><i class="fa-solid fa-backward"></i></a>
      </li>

      {% set window = 3 %}
      {% set start_page = [1, page - window]|max %}
      {% set end_page = [total_pages, page + window]|min %}

      {# 中间的页码 #}
      {% for p in range(start_page, end_page + 1) %}
        <li class="page-item {% if p == page %}active{% endif %}">
          <a class="page-link"
             href="?{{ dict(request.query_params, page=p) |urlencode }}">{{ p }}</a>
        </li>
      {% endfor %}

      {# 下一页按钮 #}
      <li class="page-item
                 {% if page >= total_pages %}disabled{% endif %}">
        <a class="page-link"
           href="?{{ dict(request.query_params, page=page+1) |urlencode }}"><i class="fa-solid fa-forward"></i></a>
      </li>

      {# 末页按钮 #}
      <li class="page-item
                 {% if page >= total_pages %}disabled{% endif %}">
        <a class="page-link"
           href="?{{ dict(request.query_params, page=total_pages) |urlencode }}"><i class="fa-solid fa-forward-fast"></i></a>
      </li>
    </ul>
  </nav>
{% endmacro %}
