import json

from fastapi import Depends, Form, Query, Response
from fastapi.responses import RedirectResponse as Redirect

from admin import schemas as sch
from admin.http import render_html, router
from admin.permissions import require_venue_manage
from apps.exhi.models import Exhibition, Joint, Ticket, Venue
from config.cities import CITIES

__all__ = ['venue_delete', 'venue_detail', 'venue_form', 'venue_list', 'venue_save']


@router.get('/venue/')
async def venue_list() -> Response:
    """展馆列表页"""
    venues = await Venue.all()
    return render_html('venue/list.html', {'venues': venues})


@router.get('/venue/detail/')
async def venue_detail(vid: int) -> Response:
    """展馆详情页"""
    venue = await Venue.get(id=vid)
    tickets = [await tk.prefetch() for tk in await Ticket.filter(vid=venue.id)]
    joints = [await jt.prefetch() for jt in await Joint.filter(vid=venue.id)]
    context = {
        'venue': venue,
        'tickets': tickets,
        'joints': joints,
        'exhibitions': await Exhibition.filter(vid=vid),
    }
    return render_html('venue/detail.html', context)


@router.get('/venue/form/')
@require_venue_manage()
async def venue_form(vid: int | None = Query(None)) -> Response:
    """展馆创建和编辑页"""
    venue, is_edit = (await Venue.get_or_none(id=vid), True) if vid else (None, False)
    return render_html(
        'venue/form.html',
        {
            'venue': venue,
            'is_edit': is_edit,
            'cities': CITIES,
            'cities_json': json.dumps(CITIES, ensure_ascii=False),
        },
    )


@router.post('/venue/save/')
@require_venue_manage()
async def venue_save(
    form: sch.VenueForm = Depends(sch.VenueForm.load),
    vid: int | None = Form(None),
) -> Response:
    """处理展馆创建和编辑表单"""
    update_data = form.model_dump(exclude_unset=True, exclude_none=True)
    province = update_data.get('province')
    city = update_data.get('city')
    if province and city and province in CITIES and city in CITIES[province]:
        coords = CITIES[province][city]
        update_data['lng'] = coords['lng']
        update_data['lat'] = coords['lat']

    if vid:
        await Venue.filter(id=vid).update(**update_data)
        await Venue.clear_cache(vid)
    else:
        vid = (await Venue.create(**update_data)).id

    return Redirect(url=f'/adm/venue/detail/?vid={vid}', status_code=303)


@router.get('/venue/delete/')
@require_venue_manage()
async def venue_delete(vid: int) -> Response:
    """删除指定展馆"""
    await (await Venue.get(id=vid)).delete()
    return Redirect(url='/adm/venue/', status_code=303)
