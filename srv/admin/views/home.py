import json
import time
from datetime import datetime, timedelta

from fastapi import Form, Request, Response
from fastapi.responses import RedirectResponse as Redirect
from jose import jwt

from admin.http import render_html, router
from admin.models import Admin
from apps.exhi.models import Venue
from apps.order.models import Order
from apps.user.models import User
from config import DEBUG, DOMAIN, JWT_ALGORITHM, SECRET_KEY, SESSION_MAX_AGE, TZ
from libs.cache import redis
from libs.state import State
from libs.utils import format_income

__all__ = ['dashboard', 'login', 'login_page', 'logout']


async def get_hourly_activity_data():
    """获取过去三天的每小时活跃度数据"""
    now = datetime.now(TZ)
    current_hour = now.hour
    labels = [f'{h:02d}:00' for h in range(24)]
    data = {
        'labels': labels,
        'today': [None] * 24,  # 初始化为 None
        'yesterday': [0] * 24,
        'day_before_yesterday': [0] * 24,
    }

    redis_key = 'Hit::Activity'

    # 获取今天的数据 (截至当前小时)
    today_dt = now
    today_keys = [f'{today_dt.strftime("%Y-%m-%d")}_{h:02d}' for h in range(current_hour + 1)]
    today_scores = await redis.zmscore(redis_key, today_keys)
    for i, score in enumerate(today_scores):
        data['today'][i] = int(score) if score else 0

    # 获取昨天和前天的数据 (完整24小时)
    days = {'yesterday': 1, 'day_before_yesterday': 2}
    for day_name, day_offset in days.items():
        day_dt = now - timedelta(days=day_offset)
        keys_to_fetch = [f'{day_dt.strftime("%Y-%m-%d")}_{h:02d}' for h in range(24)]
        scores = await redis.zmscore(redis_key, keys_to_fetch)
        data[day_name] = [int(s) if s else 0 for s in scores]

    return data


async def get_chart_data(vid: int | None = None):
    """获取图表所需的数据"""
    today = datetime.now(TZ)

    # --- 活跃时间段图 (只在全部展馆时显示) ---
    hourly_activity = await get_hourly_activity_data() if vid is None else None

    # --- 每日收入趋势 (最近14天) ---
    daily_income_labels = [(today - timedelta(days=x)).strftime('%m-%d') for x in range(14)]
    daily_income_labels.reverse()
    daily_income_data = await Order.get_daily_income_trend(days=14, vid=vid)
    daily_income_dict = {item['day'].strftime('%m-%d'): item['total'] for item in daily_income_data}
    daily_income_values = [daily_income_dict.get(date, 0) for date in daily_income_labels]

    # --- 展馆收入对比 (只在全部展馆时显示) ---
    venue_income = await Order.get_venue_income() if vid is None else None

    # --- 每周收入 (最近4周) ---
    weekly_income_labels = [(today - timedelta(weeks=x)).strftime('第%U周') for x in range(4)]
    weekly_income_labels.reverse()
    weekly_income_data = await Order.get_weekly_income_trend(weeks=4, vid=vid)
    weekly_income_dict = {item['week'].strftime('第%U周'): item['total'] for item in weekly_income_data}
    weekly_income_values = [weekly_income_dict.get(label, 0) for label in weekly_income_labels]

    # --- 每月收入 (最近12个月) ---
    monthly_income_labels = []
    year, month = today.year, today.month
    for _ in range(12):
        monthly_income_labels.append(f'{year}-{month:02d}')
        month -= 1
        if month == 0:
            month = 12
            year -= 1
    monthly_income_labels.reverse()
    monthly_income_data = await Order.get_monthly_income_trend(months=12, vid=vid)
    monthly_income_dict = {item['month'].strftime('%Y-%m'): item['total'] for item in monthly_income_data}
    monthly_income_values = [monthly_income_dict.get(label, 0) for label in monthly_income_labels]

    return {
        'hourly_activity': hourly_activity,
        'daily_income': {'labels': daily_income_labels, 'data': daily_income_values},
        'venue_income': venue_income,
        'weekly_income': {'labels': weekly_income_labels, 'data': weekly_income_values},
        'monthly_income': {'labels': monthly_income_labels, 'data': monthly_income_values},
    }


@router.get('/login')
async def login_page(request: Request) -> Response:
    """渲染后台登录页面"""
    if request.user.is_authenticated:
        return Redirect(url='/adm/', status_code=303)
    return render_html('login.html')


@router.post('/login')
async def login(username: str = Form(...), password: str = Form(...)) -> Response:
    """处理后台登录表单，校验用户名和密码，登录成功后跳转到后台主页"""
    error = None
    manager = None
    if username.isdecimal() and len(username) == 11:
        # 如果用户名是手机号格式，尝试使用手机号查找管理员
        manager = await Admin.get_or_none(phone=username)

    if not manager:
        manager = await Admin.get_or_none(username=username)

    if not manager:
        error = '用户名或手机号错误'
        return render_html('login.html', {'error': error})

    if not manager.verify_password(password):
        error = '用户密码错误'
    else:
        now = int(time.time())
        payload = {
            'uid': manager.id,
            'username': manager.username,
            'admin': True,
            'iat': now,
            'exp': now + SESSION_MAX_AGE,
        }
        jwt_token = jwt.encode(payload, SECRET_KEY, JWT_ALGORITHM)
        response = Redirect(url='/adm/', status_code=303)
        response.set_cookie(
            'Authorization',
            f'Bearer {jwt_token}',
            max_age=SESSION_MAX_AGE,
            domain=None if DEBUG else f'.{DOMAIN}',
            secure=not DEBUG,
            httponly=False,
            samesite='lax',
        )
        return response

    return render_html('login.html', {'error': error})


@router.get('/logout')
async def logout() -> Response:
    """注销后台登录，清除 session 并跳转到登录页"""
    response = Redirect(url='/adm/login', status_code=303)
    response.delete_cookie('Authorization', domain=None if DEBUG else f'.{DOMAIN}', samesite='lax')
    return response


@router.get('/')
async def dashboard(request: Request) -> Response:
    """后台首页仪表盘"""
    cur_adm = State.get('user')
    if cur_adm.can_view_all_data:
        vid_param = request.query_params.get('vid')
        vid = int(vid_param) if vid_param and vid_param.isdigit() else None
    else:
        vid = cur_adm.vid

    # 获取展馆列表
    venues = await Venue.all() if cur_adm.can_view_all_data else []

    # 获取数据
    income_yesterday = await Order.get_yesterday_income(vid=vid)
    income_this_month = await Order.get_this_month_income(vid=vid)
    n_order = await Order.filter(vid=vid).count() if vid else await Order.all().count()

    chart_data = await get_chart_data(vid=vid)

    context = {
        'venues': venues,
        'selected_vid': vid,
        'cur_adm': cur_adm,
        'n_user': await User.all().count(),
        'n_order': n_order,
        'income_yesterday': format_income(income_yesterday),
        'income_this_month': format_income(income_this_month),
        'chart_data': json.dumps(chart_data),
    }
    return render_html('dashboard.html', context)
