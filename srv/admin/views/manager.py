from fastapi import Depends, HTTPException, Response
from fastapi.responses import RedirectResponse as Redirect
from tortoise.transactions import in_transaction

from admin import schemas as sch
from admin.http import render_html, router
from admin.models import Admin, Role
from admin.permissions import filter_venues_by_permission, require_admin_manage
from apps.exhi.models import Venue
from libs.state import State

__all__ = ['manager_delete', 'manager_form', 'manager_list', 'manager_save']


@router.get('/manager/')
@require_admin_manage()
async def manager_list() -> Response:
    """管理员用户列表页"""
    cur_adm = State.get('user')

    # 根据用户角色过滤管理员列表
    if cur_adm.role in [Role.SUPER, Role.ADMIN]:
        managers = await Admin.all()
    elif cur_adm.role == Role.MANAGER:
        # 店长只能看到自己门店的员工和自己
        managers = await Admin.filter(vid=cur_adm.vid, role__in=[Role.MANAGER, Role.STAFF])
    else:
        # 员工和运营只能看到自己
        managers = [cur_adm]

    for manager in managers:
        manager.organization = await manager.belonged()

    return render_html('manager/list.html', {'managers': managers, 'cur_adm': cur_adm})


@router.get('/manager/form/')
@require_admin_manage()
async def manager_form(manager_id: int | None = None) -> Response:
    """管理员用户创建和编辑页"""
    cur_adm = State.get('user')

    # 获取可用的门店列表
    vids = await filter_venues_by_permission(cur_adm)
    venues = await Venue.filter(id__in=vids) if vids else []

    manager = None
    is_edit = False

    if manager_id:
        manager = await Admin.get_or_none(id=manager_id)
        if not manager:
            raise HTTPException(status_code=404, detail='管理员不存在')

        # 检查是否有权限编辑此管理员
        if cur_adm.role == Role.MANAGER:
            if manager.vid != cur_adm.vid:
                raise HTTPException(status_code=403, detail='无权限编辑此管理员')

        is_edit = True

    context = {
        'manager': manager,
        'is_edit': is_edit,
        'venues': venues,
        'available_roles': [cur_adm.role] if manager_id == cur_adm.id else cur_adm.can_create_roles,
        'cur_adm': cur_adm,
        'Role': Role,
    }

    return render_html('manager/form.html', context)


@router.post('/manager/save/')
@require_admin_manage()
async def manager_save(form: sch.AdminForm = Depends(sch.AdminForm.load)) -> Response:
    """处理管理员用户创建和编辑表单"""
    cur_adm = State.get('user')

    # 检查是否有权限创建/编辑此角色
    if not cur_adm.can_create_role(form.role):
        raise HTTPException(status_code=403, detail='无权限创建此角色的账号')

    # 检查门店权限
    if form.role in [Role.MANAGER, Role.STAFF]:
        if not cur_adm.can_manage_venue(form.vid):
            raise HTTPException(status_code=403, detail='无权限管理此门店')

    if form.manager_id:
        # 编辑现有管理员
        manager = await Admin.get_or_none(id=form.manager_id)
        if not manager:
            raise HTTPException(status_code=404, detail='管理员不存在')

        # 检查权限
        if not cur_adm.can_edit_adm(manager):
            raise HTTPException(status_code=403, detail='您没有权限执行此操作，请联系更高级别的管理员来处理。')

        update_data = form.model_dump(exclude={'manager_id', 'password'}, exclude_none=True)
        if form.password:
            update_data['password'] = Admin.make_password(form.password)

        async with in_transaction():
            await manager.update_from_dict(update_data)
            await manager.set_adm_user()
        await Admin.clear_cache(form.manager_id)
    else:
        # 创建新管理员
        update_data = form.model_dump(exclude={'manager_id'}, exclude_none=True)
        if form.password:
            update_data['password'] = Admin.make_password(form.password)
        async with in_transaction():
            manager = await Admin.create(**update_data)
            await manager.set_adm_user()

    return Redirect(url='/adm/manager/', status_code=303)


@router.get('/manager/delete/')
@require_admin_manage()
async def manager_delete(manager_id: int) -> Response:
    """删除指定管理员用户"""
    cur_adm = State.get('user')

    manager = await Admin.get_or_none(id=manager_id)
    if not manager:
        raise HTTPException(status_code=404, detail='管理员不存在')

    # 检查权限
    if not cur_adm.can_delete_adm(manager):
        raise HTTPException(status_code=403, detail='您没有权限执行此操作，请联系更高级别的管理员来处理。')

    async with in_transaction():
        await manager.unset_adm_user()
        await manager.delete()
    return Redirect(url='/adm/manager/', status_code=303)
