import json
from datetime import date

from fastapi import File, Form, HTTPException, Query, Response, UploadFile
from fastapi.responses import RedirectResponse as Redirect

from admin.http import render_html, router
from admin.permissions import require_ticket_manage
from admin.schemas import JointForm
from apps.exhi.models import Exhibition, Joint, Venue
from config import ALLOWED_TYPES, BANNER_PREFIX, DETAIL_PREFIX, MAX_SIZE, THUMB_PREFIX
from libs.utils import file_type, save_upfile

__all__ = ['joint_delete', 'joint_detail', 'joint_form', 'joint_list', 'joint_save', 'joint_themes']


@router.get('/joint/')
@require_ticket_manage()
async def joint_list() -> Response:
    """联票列表页"""
    joints = []
    for j in await Joint.all():
        await j.prefetch()
        joints.append(j)

    return render_html('joint/list.html', {'joints': joints})


@router.get('/joint/detail/')
async def joint_detail(jid: int) -> Response:
    """联票详情页"""
    joint = await Joint.get(id=jid)
    await joint.prefetch()
    return render_html('joint/detail.html', {'joint': joint})


@router.get('/joint/form/')
@require_ticket_manage()
async def joint_form(jid: int | None = Query(None), vid: int | None = Query(None)) -> Response:
    """联票创建和编辑页"""
    joint, is_edit = (await Joint.get_or_none(id=jid), True) if jid else (None, False)
    if joint:
        vid = joint.vid
    venues = [await Venue.get(id=vid)] if vid else await Venue.all()
    return render_html(
        'joint/form.html',
        {
            'joint': joint,
            'is_edit': is_edit,
            'venues': venues,
            'today': date.today().isoformat(),
        },
    )


@router.post('/joint/save/')
@require_ticket_manage()
async def joint_save(  # noqa: C901
    title: str = Form(..., max_length=64),
    vid: int = Form(...),
    eids: list[int] = Form(...),
    start: str = Form(None),
    end: str = Form(None),
    prices: str = Form('{}'),
    thumbnail: UploadFile = File(None),
    banner: UploadFile = File(None),
    details: list[UploadFile] = File([]),
    thumbnail_path: str = Form(None),
    banner_path: str = Form(None),
    details_paths: list[str] = Form([]),
    jid: int | None = Form(None),
    n_unit: int = Form(2),
) -> Response:
    """处理联票创建和编辑表单"""
    form_data = {
        'title': title,
        'vid': vid,
        'eids': eids,
        'prices': json.loads(prices),
        'start': start,
        'end': end,
        'jid': jid,
        'n_unit': n_unit,
    }
    form = JointForm.load_from_json(form_data)

    all_files = [thumbnail, banner, *details]
    for upfile in all_files:
        if not hasattr(upfile, 'filename') or not upfile.filename:
            continue
        extension = file_type(upfile)
        if extension not in ALLOWED_TYPES:
            raise HTTPException(status_code=400, detail=f'文件类型 {extension} 不支持，仅支持 jpg')
        fsz = upfile.size or 0
        if not (0 < fsz <= MAX_SIZE):
            raise HTTPException(
                status_code=400, detail=f'文件 {upfile.filename} 大小超过限制: {round(fsz / 1024 / 1024, 1)}MB'
            )

    update_data = {
        'title': form.title,
        'vid': form.vid,
        'eids': form.eids,
        'start': form.start,
        'end': form.end,
        'prices': form.prices,
        'n_unit': form.n_unit,
    }

    if thumbnail and hasattr(thumbnail, 'filename') and thumbnail.filename:
        update_data['thumbnail'] = await save_upfile(thumbnail, THUMB_PREFIX)
    elif thumbnail_path:
        update_data['thumbnail'] = thumbnail_path

    if banner and hasattr(banner, 'filename') and banner.filename:
        update_data['banner'] = await save_upfile(banner, BANNER_PREFIX)
    elif banner_path:
        update_data['banner'] = banner_path

    new_details = details_paths.copy()
    for upfile in details:
        if hasattr(upfile, 'filename') and upfile.filename:
            new_details.append(await save_upfile(upfile, DETAIL_PREFIX))
    update_data['details'] = new_details

    if form.jid:
        await Joint.filter(id=form.jid).update(**update_data)
        await Joint.clear_cache(form.jid)
        new_jid = form.jid
    else:
        joint = await Joint.create(**update_data)
        new_jid = joint.id

    return Redirect(url=f'/adm/joint/detail/?jid={new_jid}', status_code=303)


@router.get('/joint/delete/')
@require_ticket_manage()
async def joint_delete(jid: int) -> Response:
    """删除指定联票"""
    await (await Joint.get(id=jid)).delete()
    return Redirect(url='/adm/joint/', status_code=303)


@router.get('/joint/themes/')
async def joint_themes():
    """获取指定展馆的展览列表"""
    themes = await Exhibition.all().order_by('-id')
    return {t.id: t.name for t in themes}
