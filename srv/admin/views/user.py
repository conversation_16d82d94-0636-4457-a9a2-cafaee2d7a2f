import math

from fastapi import Depends, Form, Query, Response
from fastapi.responses import RedirectResponse as Redirect
from tortoise.expressions import Q

from admin import schemas as sch
from admin.http import render_html, router
from apps.order.models import Order
from apps.user.models import User
from config import PAGE_SIZE
from libs.state import State

__all__ = ['user_delete', 'user_detail', 'user_form', 'user_list', 'user_save']


@router.get('/user/')
async def user_list(page: int = Query(1, ge=1), q: str = Query(None)) -> Response:
    """用户列表页，支持分页和模糊搜索"""
    cur_adm = State.get('user')
    query = User.all()

    if cur_adm.vid:
        uids = await Order.filter(vid=cur_adm.vid).order_by('uid').distinct().values_list('uid', flat=True)
        query = query.filter(id__in=uids)

    if q:
        query = query.filter(Q(name__icontains=q) | Q(phone__icontains=q))

    total_count = await query.count()
    total_pages = math.ceil(total_count / PAGE_SIZE)
    users = await query.limit(PAGE_SIZE).offset((page - 1) * PAGE_SIZE)

    context = {
        'users': users,
        'page': page,
        'total_pages': total_pages,
        'q': q,
    }
    return render_html('user/list.html', context)


@router.get('/user/detail/')
async def user_detail(user_id: int) -> Response:
    """用户详情页"""
    user = await User.get(id=user_id)
    orders = await Order.filter(uid=user_id).order_by('-created')
    for order in orders:
        await order.prefetch()
    return render_html('user/detail.html', {'user': user, 'orders': orders})


@router.get('/user/form/')
async def user_form(user_id: int | None = Query(None)) -> Response:
    """用户创建和编辑页"""
    user, is_edit = (await User.get(id=user_id), True) if user_id else (None, False)
    return render_html('user/form.html', {'user': user, 'is_edit': is_edit})


@router.post('/user/save/')
async def user_save(
    form: sch.UserForm = Depends(sch.UserForm.load),
    user_id: int | None = Form(None),
) -> Response:
    """处理用户创建和编辑表单"""
    update_data = form.model_dump(exclude_unset=True, exclude_none=True)
    if user_id:
        await User.filter(id=user_id).update(**update_data)
        await User.clear_cache(user_id)
    else:
        user = await User.create(**update_data)
        user_id = user.id

    return Redirect(url=f'/adm/user/detail/?user_id={user_id}', status_code=303)


@router.get('/user/delete/')
async def user_delete(user_id: int) -> Response:
    """删除指定用户"""
    await (await User.get(id=user_id)).delete()
    return Redirect(url='/adm/user/', status_code=303)
