from pathlib import Path

from apps.exhi.models import Exhibition, Joint
from apps.order.models import Order
from apps.order.schemas import OrderStatus
from config import BANNER_PREFIX, DETAIL_PREFIX, THUMB_PREFIX, UPLOAD_DIR


async def delete_unused_images():
    """删除未使用的展览素材"""
    in_used = set()
    updir = Path(UPLOAD_DIR)
    trash = updir / 'trash'
    trash.mkdir(exist_ok=True)

    # 收集所有使用中的素材
    for exhi in await Exhibition.all():
        in_used.add(updir / Path(exhi.thumbnail).name)
        in_used.add(updir / Path(exhi.banner).name)
        in_used.update(updir / Path(path).name for path in exhi.details)
    for joint in await Joint.all():
        in_used.add(updir / Path(joint.thumbnail).name)
        in_used.add(updir / Path(joint.banner).name)
        in_used.update(updir / Path(path).name for path in joint.details)
    for order in await Order.all():
        if order.status not in [OrderStatus.canceled, OrderStatus.closed, OrderStatus.failed]:
            archive = dict(order.archive)
            if thumb := archive.get('thumbnail'):
                in_used.add(updir / Path(thumb).name)

    # 删除未使用的素材
    thumbs = set(updir.glob(f'{THUMB_PREFIX}*'))
    banners = set(updir.glob(f'{BANNER_PREFIX}*'))
    details = set(updir.glob(f'{DETAIL_PREFIX}*'))
    imgs = thumbs | banners | details - in_used
    for path in imgs:
        if path not in in_used:
            dst = trash / Path(path).name
            path.rename(dst)
            print(f'Drop {path.relative_to(updir)} to trash')
