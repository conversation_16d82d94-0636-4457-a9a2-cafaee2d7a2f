from logging import config as logging_config

from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.staticfiles import StaticFiles
from pydantic import ValidationError
from tortoise.contrib.fastapi import register_tortoise
from tortoise.exceptions import DoesNotExist

import config as cfg
from admin import router as admin_router
from admin.http import http_exception_handler, not_exist_handler, not_found_handler
from apps import router as api_router
from libs.jwt import JWTMiddleware
from libs.middleware import ExceptionMiddleware, register_middlewares, validation_exception_handler
from libs.state import StateMiddleware
from tasks import lifespan

logging_config.dictConfig(cfg.LOGGING)

if cfg.DEBUG:
    app = FastAPI(title=cfg.APP_NAME, debug=True, lifespan=lifespan)
else:
    app = FastAPI(title=cfg.APP_NAME, debug=False, lifespan=lifespan, docs_url=None, redoc_url=None, openapi_url=None)

# 注册中间件
register_middlewares(app, ExceptionMiddleware, StateMiddleware, JWTMiddleware)

# 添加异常处理
app.add_exception_handler(DoesNotExist, not_exist_handler)  # type: ignore
app.add_exception_handler(HTTPException, http_exception_handler)  # type: ignore
app.add_exception_handler(404, not_found_handler)  # type: ignore
app.add_exception_handler(ValidationError, validation_exception_handler)  # type: ignore


# 注册数据库
register_tortoise(app=app, config=cfg.DATABASE, generate_schemas=False, add_exception_handlers=cfg.DEBUG)

# 设置路由
app.include_router(api_router, prefix='/api', tags=['API 接口'])
app.include_router(admin_router, prefix='/adm', tags=['后台管理'])

# 挂载静态文件
app.mount('/static', StaticFiles(directory=cfg.STATIC_DIR), name='static')


if __name__ == '__main__':
    import os
    from pathlib import Path

    import uvicorn

    # 写入 PID 文件
    if PID_PATH := getattr(cfg, 'PID_PATH', None):
        pid_file = Path(PID_PATH)
        pid_file.parent.mkdir(parents=True, exist_ok=True)
        pid_file.write_text(str(os.getpid()))

    # 启动服务
    uvicorn.run('main:app', host='127.0.0.1', port=8002, **cfg.UVICORN)  # type: ignore
