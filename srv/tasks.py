import asyncio
import time
from contextlib import asynccontextmanager

from fastapi import FastAPI

from apps.order.models import Order


async def process_unpaid_orders():
    """处理未支付的订单"""
    while True:
        await asyncio.sleep(1)  # 先等待1秒,避免CPU空转

        now = int(time.time())
        if now % 60 == 0:  # 每分钟执行一次
            await Order.close_expired_orders()

        if now % 3600 == 0:  # 每小时执行一次
            await Order.delete_closed_orders()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    task = asyncio.create_task(process_unpaid_orders())

    yield  # 应用运行期间保持运行

    # 关闭前执行
    task.cancel()
    try:
        await task
    except asyncio.CancelledError:
        pass
