/* From base.html */
:root {
  --bs-success-rgb: 0, 170, 0;
  --img-bg-color: #333;
}

body {
  display: flex;
  min-height: 100vh;
  flex-direction: column;
  color: var(--bs-body-color);
  background-color: #f0f0f0;
}

thead tr th {
  color: var(--bs-body-color) !important;
}

td > a,
td li > a {
  text-decoration: none;
  color: var(--bs-primary);
  border-bottom: 1px solid var(--bs-primary);
}
td > a:hover,
td li > a:hover {
  color: #0050a5;
  border-bottom: 1.5px solid #0050a5;
}

.w-5 {
  width: 5% !important;
}
.w-10 {
  width: 10% !important;
}
.w-15 {
  width: 15% !important;
}
.w-20 {
  width: 20% !important;
}
.w-30 {
  width: 30% !important;
}

.shadow {
  box-shadow: 3px 3px 9px rgba(0, 0, 0, 0.1) !important;
}

.main-container {
  display: flex;
  flex: 1;
}

.sidebar {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  width: 190px;
  padding: 1rem 1rem 0 1rem;
  color: white;
  background: #343a40;
  overflow-y: auto;
  z-index: 1030;
  transition: transform 0.3s ease-in-out;
}
.sidebar .sidebar-header {
  padding: 1rem;
  text-align: center;
}
.sidebar .sidebar-header img {
  max-width: 80%;
  margin-bottom: 2rem;
}
.sidebar .sidebar-header h4 {
  margin-bottom: 1rem;
}
.sidebar .nav-link {
  color: #adb5bd;
  border-radius: 10px;
}
.sidebar .nav-link.active,
.sidebar .nav-link:hover {
  color: #fff;
  background-color: var(--bs-primary);
}
.sidebar .nav-header {
  margin-top: 15px;
  border-bottom: #888 1px solid;
  color: var(--bs-info);
  font-weight: bold;
}
.sidebar .nav-item {
  margin: 3px 0;
}

.sidebar-footer {
  margin-top: auto;
  padding-bottom: 1rem;
}

.btn-close-sidebar {
  background: transparent;
  border: none;
  color: var(--bs-secondary);
  font-size: 1.5rem;
}

/* 顶部导航栏 */
.navbar {
  position: fixed;
  top: 0;
  left: 190px;
  right: 0;
  height: 60px;
  z-index: 1020;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);
  transition: left 0.3s ease-in-out;
}
.navbar .breadcrumb-item > a {
  border: 1px solid rgba(0, 0, 0, 0);
  padding: 2px 8px;
  color: var(--bs-primary);
  text-decoration: none;
  transition: all 0.2s ease-in-out;
}
.navbar .breadcrumb-item > a:hover,
.navbar .breadcrumb-item > a:active {
  border: 1px solid var(--bs-primary);
  padding: 2px 8px;
  border-radius: 5px;
  color: white;
  background-color: var(--bs-primary);
}
.navbar .breadcrumb-item.active span {
  border: 1px solid rgba(0, 0, 0, 0);
  padding-left: 8px;
}
.navbar .container-fluid {
  padding-right: 2rem;
}
.navbar .navbar-brand {
  display: none;
}
.navbar .navbar-nav .nav-link {
  color: #495057;
}
.navbar .navbar-nav .nav-link:hover {
  color: #000;
}
.navbar-toggler {
  display: none;
}

/* 内容区 */
.content {
  flex: 1;
  padding: 1.5rem;
  margin: 80px 20px 0 210px;
  border-radius: 15px 15px 0 0;
  background-color: white;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);
  transition: margin-left 0.3s ease-in-out;
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1029;
  display: none;
}
.overlay.show {
  display: block;
}

@media (max-width: 768px) {
  body {
    background-color: white;
  }
  main {
    background-color: white;
  }
  .sidebar {
    transform: translateX(-100%);
  }
  .sidebar.show {
    transform: translateX(0);
  }
  .navbar {
    left: 0;
  }
  .navbar-toggler {
    display: block;
  }
  .main-container {
    display: block;
  }
  .content {
    margin: 80px 0;
    padding: 0px;
    border-radius: 0;
    box-shadow: none;
  }
}

/* From login.html */
.login-body {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  background-color: #e5e0ff;
  overflow: clip;
  position: relative;
}
.login-body::before,
.login-body::after {
  content: '';
  position: absolute;
  width: 1200px;
  height: 1200px;
  background-image: url('/static/img/bg.svg');
  background-size: cover;
  z-index: 0;
}
.login-body::before {
  top: -80%;
  right: -30%;
  transform: translate(0, 0) rotate(0deg);
}
.login-body::after {
  bottom: -80%;
  left: -30%;
  transform: translate(0, 0) rotate(180deg);
}

.login-card {
  position: relative;
  width: 100%;
  max-width: 400px;
  padding: 2rem;
  border-radius: 1.5rem;
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.2);
  z-index: 1;
}

.list-banner {
  width: 100px;
  height: 50px;
  object-fit: fill;
}

.btn-group .btn {
  margin-right: 5px;
}

/* 设置页面样式 */
.settings-upimg-preview {
  display: flex;
  max-height: 210px;
  min-height: 200px;
  border: 2px solid #dee2e6;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background-color: var(--img-bg-color);
}

.settings-upimg-preview > img {
  align-items: center;
  justify-content: center;
  height: 100%;
  max-height: 200px;
  border-radius: 6px;
}

.settings-slide-placeholder {
  max-height: 210px;
  min-height: 200px;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
}

.settings-text-content {
  max-height: 210px;
  min-height: 200px;
  overflow-y: auto;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 5px;
  border: 1px solid #dee2e6;
}

/* 设置页面并排 card 高度一致 */
.row .card {
  height: 100%;
}

.ticket-checkbox-list {
  max-height: 150px; /* 设置最大高度 */
  overflow-y: auto; /* 垂直滚动 */
  border: 1px solid #dee2e6;
  border-radius: 5px;
  padding: 10px;
}

.z-top {
  z-index: 9999;
}

.unset-bg {
  background-color: initial !important;
}

.btn-toolbar {
  min-width: 4em;
}

.fs-7 {
  font-size: 0.8rem !important;
}

.bg-sliver {
  background-color: #fdfdfd !important;
}

/* Custom date input placeholder */
.date-input-wrapper {
  position: relative;
}

.date-input-wrapper::before {
  content: '所有日期';
  position: absolute;
  top: 50%;
  left: 12px;
  transform: translateY(-50%);
  color: #6c757d;
  pointer-events: none;
  z-index: 1;
}

/* Hide browser's placeholder by making the text transparent */
.date-input-wrapper:not(.has-value) > input {
  color: transparent;
}

/* When a date is selected, hide our custom placeholder */
.date-input-wrapper.has-value::before {
  display: none;
}
