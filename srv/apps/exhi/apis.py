from fastapi import APIRouter

import config as cfg
from admin.models import Setting
from apps.exhi.models import Joint, Ticket
from apps.exhi.schemas import TicketCategory as Catg
from config import BRAND_BG, LOGO, SLIDES
from libs.utils import up_url

router = APIRouter()


@router.get('/settings')
async def get_settings():
    """获取设置"""
    st_names = ['brand_intro', 'theater_enable', 'user_notice', 'privacy']
    settings = await Setting.values(*st_names)
    settings.set('slides', [up_url(slide) for slide in SLIDES])
    settings.set('logo', up_url(LOGO))
    settings.set('brand_bg', up_url(BRAND_BG))
    return settings


@router.get('/exhi_rcmd')
async def exhi_rcmd():
    """热展推荐"""
    settings = await Setting.values('exhi_rcmd')
    tk_rcmd = await Ticket.in_times(id__in=settings.exhi_rcmd, catg=Catg.ticket).order_by('-id')
    return {'rcmds': [await tk.detail() for tk in tk_rcmd]}


@router.get('/ticket/list')
async def ticket_list(city: str | None = None, vid: int | None = None, page: int = 1):
    """展览列表"""
    page = page if page > 0 else 1
    offset = (page - 1) * cfg.PAGE_SIZE
    if city:
        tickets = await Ticket.in_city(city, '-id', cfg.PAGE_SIZE, offset)
    elif vid:
        tickets = await Ticket.in_times(vid=vid, catg=Catg.ticket).order_by('-id').limit(cfg.PAGE_SIZE).offset(offset)
    else:
        tickets = await Ticket.in_times(catg=Catg.ticket).order_by('-id').limit(cfg.PAGE_SIZE).offset(offset)
    return {'tickets': [await exhi.detail() for exhi in tickets]}


@router.get('/ticket/detail')
async def ticket_detail(tid: int):
    """展览详情"""
    if exhi := await Ticket.get_or_none(id=tid):
        return await exhi.detail()
    else:
        return {'error': '展览不存在或已下线'}


@router.get('/ticket/price')
async def ticket_price(tid: int):
    """展票价格"""
    if tk := await Ticket.get_or_none(id=tid):
        return {'title': tk.title, 'prices': tk.prices, 'catg': tk.catg}
    else:
        return {'error': '展览不存在或已下线'}


# TODO: 暂未开放
# @router.get('/theater/lsit')
# async def theater_list():
#     """空间剧场列表"""
#     pass


@router.get('/joint/list')
async def joint_list(city: str | None = None, vid: int | None = None, page: int = 1):
    """联票列表"""
    page = page if page > 0 else 1
    offset = (page - 1) * cfg.PAGE_SIZE
    if city:
        joints = await Joint.in_city(city, '-id', cfg.PAGE_SIZE, offset)
    elif vid:
        joints = await Joint.in_times(vid=vid).order_by('-id').limit(cfg.PAGE_SIZE).offset(offset)
    else:
        joints = await Joint.in_times().order_by('-id').limit(cfg.PAGE_SIZE).offset(offset)
    return {'joints': [await joint.detail() for joint in joints]}


@router.get('/joint/detail')
async def joint_detail(jid: int):
    """联票详情"""
    if joint := await Joint.get_or_none(id=jid):
        return await joint.detail()
    else:
        return {'error': '展览不存在或已下线'}


@router.get('/joint/price')
async def joint_price(jid: int):
    """联票价格"""
    if joint := await Joint.get_or_none(id=jid):
        await joint.prefetch()
        return {
            'title': joint.title,
            'prices': joint.prices,
            'catg': Catg.joint,
            'n_unit': joint.n_unit,
            'themes': [e.sample() for e in joint.exhibitions],
        }
    else:
        return {'error': '展览不存在或已下线'}


@router.get('/reserve/list')
async def reserve_list():
    """预约列表"""
    tickets = await Ticket.filter(catg=Catg.reserve).order_by('-id')
    return {'tickets': [await tk.detail() for tk in tickets]}
