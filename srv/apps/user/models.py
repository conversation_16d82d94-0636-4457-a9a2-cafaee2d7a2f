import logging
from typing import ClassVar

from starlette.authentication import BaseUser
from tortoise import fields
from tortoise.transactions import atomic, in_transaction

from config import PAGE_SIZE
from libs.orm import Model

inflog = logging.getLogger('info')


class User(Model, BaseUser):
    id = fields.IntField(pk=True)
    name = fields.CharField(max_length=64, description='用户名')
    avatar = fields.CharField(max_length=256, null=True, description='头像')
    openid = fields.CharField(max_length=64, unique=True, description='微信 Open ID')
    union_id = fields.CharField(max_length=64, unique=True, null=True, description='微信的 Union ID')
    phone = fields.CharField(max_length=11, unique=True, null=True, description='手机号')
    is_adm = fields.BooleanField(default=False, description='是否是管理员')

    # 用户状态
    created = fields.DatetimeField(auto_now_add=True, description='创建时间')

    class Meta:  # type: ignore
        table = 'users'
        ordering: ClassVar[list[str]] = ['-id']

    @property
    def is_authenticated(self):
        return True

    @property
    def display_name(self):
        return self.name

    @property
    def identity(self):
        return self.openid

    async def set_phone(self, phone: str | None):
        """设置手机号"""
        from admin.models import Admin

        if phone == self.phone:
            return
        elif phone is None:
            # 解除绑定
            self.phone = None  # type: ignore
            self.is_adm = False
            await self.save()
        elif older := await User.get_or_none(phone=phone):
            async with in_transaction():
                # 解除旧绑定关系
                older.phone = None  # type: ignore
                older.is_adm = False
                await older.save()
                # 设置新绑定关系
                self.phone = phone
                self.is_adm = await Admin.check_phone(phone)
                await self.save()
            inflog.info(f'User({older.id}) 的手机号 {phone} 绑定至新账户 User({self.id})')
        else:
            # 新手机号，直接绑定
            self.phone = phone
            self.is_adm = await Admin.check_phone(phone)
            await self.save()

    def orders(self, status: str | None = None, limit: int = PAGE_SIZE, offset: int = 0):
        """获取用户订单列表"""
        from apps.order.models import Order

        kwargs = {'uid': self.id, 'status': status} if status else {'uid': self.id}
        query = Order.filter(**kwargs)

        return query.order_by('-created').limit(limit).offset(offset)

    @atomic()
    async def delete(self, *args, **kwargs):
        """删除用户"""
        # 删除用户订单
        from apps.order.models import Order

        await Order.filter(uid=self.id).delete()
        return await super().delete(*args, **kwargs)
