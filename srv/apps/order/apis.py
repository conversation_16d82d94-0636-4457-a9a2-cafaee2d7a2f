import datetime
import logging
import time
import uuid

from fastapi import APIRouter, Request
from tortoise.exceptions import DoesNotExist
from tortoise.expressions import Q

import config as cfg
from apps.exhi.models import Joint, Ticket
from apps.exhi.schemas import TicketCategory as Catg
from apps.order.models import Order, OrderStatus
from apps.order.schemas import CheckinForm, OidForm, OrderFilter, OrderSubmitForm, ReserveForm
from libs import wxmp
from libs.attrdict import AttrDict
from libs.http import abort, fail, success
from libs.state import State
from libs.wxmp import wxpay

router = APIRouter()
logger = logging.getLogger('payment')


@router.post('/order/submit')
async def submit_order(form: OrderSubmitForm):
    """创建订单"""
    try:
        tk: Ticket | Joint
        if form.catg == Catg.joint:
            tk = await Joint.get(id=form.tid)
            eids = tk.eids
        else:
            tk = await Ticket.get(id=form.tid)
            eids = [tk.eid]
    except DoesNotExist:
        return abort(404, '展票 ID 错误，请刷新后重试')

    # 检查展票价格
    unit_price = tk.price_of(form.timeslot, form.grade)
    if unit_price <= 0 or unit_price * form.quantity != form.amount:
        return abort(410, '价格或有变动，请刷新后重试')

    try:
        # 创建订单
        user = State.get('user')
        archive_data = await tk.archive(form.timeslot, form.grade, form.sub_tids)

        order = await Order.create(
            uid=user.id,
            tid=form.tid,
            vid=tk.vid,
            eids=eids,
            phone=user.phone,
            catg=form.catg,
            quantity=form.quantity,
            amount=form.amount,
            sub_tids=form.sub_tids,
            timeslot=form.timeslot,
            grade=form.grade,
            archive=archive_data,
        )
    except Exception as e:
        logger.error(f'创建订单失败: {e}')
        return abort(500, f'创建订单失败: {e}')

    try:
        # 调用微信支付
        await order.prefetch()
        code, message = wxpay.pay(
            description=await order.description(),
            out_trade_no=order.trade_no,
            amount={'total': int(form.amount * 100), 'currency': 'CNY'},
            payer={'sp_openid': user.openid},
            time_expire=order.expire_time.isoformat(),
            notify_url=cfg.WX_PAYMENT.notify_url,
            sub_appid=order.venue.appid,  # 子商户应用ID (服务商模式)
            sub_mchid=order.venue.mchid,  # 子商户的商户号 (服务商模式)
            pay_type=cfg.WX_PAYMENT.wechatpay_type,
        )
        logger.info(f'订单创建成功: {order.trade_no=}; {code=}; {message=}')
    except Exception as e:
        logger.error(f'发起支付失败: {e}')
        return abort(500, f'发起支付失败: {e}')

    result = AttrDict(message)
    if code == 200:
        # 保存 prepay_id
        order.wx_prepay_id = result.prepay_id
        await order.save()
        appid = cfg.WX_PAYMENT.appid
        timestamp = str(int(time.time()))
        nonce = uuid.uuid4().hex
        package = f'prepay_id={result.prepay_id}'
        sign = wxmp.payment_signature(appid, timestamp, nonce, package)

        # 返回支付参数
        return {
            'orderId': order.id,
            'timeStamp': timestamp,
            'nonceStr': nonce,
            'package': package,
            'signType': 'RSA',
            'paySign': sign,
        }
    else:
        logger.error(f'发起支付失败: {result}')
        return abort(code or 400, result.message or '发起支付失败')


@router.get('/order/pay')
async def pay_order(order_id: int):
    """重新发起支付 (用户提交订单时未支付，后在订单处重新发起支付时使用)"""
    order = await Order.get(id=order_id)
    # 返回支付参数
    appid = cfg.WX_PAYMENT.appid
    timestamp = str(int(time.time()))
    nonce = uuid.uuid4().hex
    package = f'prepay_id={order.wx_prepay_id}'
    sign = wxmp.payment_signature(appid, timestamp, nonce, package)

    return {
        'timeStamp': timestamp,
        'nonceStr': nonce,
        'package': package,
        'signType': 'RSA',
        'paySign': sign,
    }


@router.post('/payment/callback')
async def payment_callback(request: Request):
    """支付回调处理"""
    # 解析回调数据
    headers = dict(request.headers)
    body = (await request.body()).decode('utf-8')
    data = wxpay.callback(headers, body)

    if not isinstance(data, dict) or 'resource' not in data:
        # 微信支付有时会故意生成错误签名，以探测商户是否正确地验证了签名，此时不用打印日志
        if not headers.get('wechatpay-signature', '').startswith('WECHATPAY/SIGNTEST'):
            logger.error(f'支付回调数据格式错误:\n\t{headers=}\n\t{body=}')
        return fail(400, '支付回调数据格式错误')

    logger.info(f'收到支付回调数据: {data}')
    result = AttrDict(data['resource'])  # type: ignore

    try:
        # 查找订单
        order = await Order.get(trade_no=result.out_trade_no)

        match result.trade_state:
            case 'SUCCESS':
                logger.info(f'订单 {result.out_trade_no} 支付成功')
                await order.pay_success(result.transaction_id, result.success_time)  # 支付成功
            case 'CLOSED' | 'REVOKED':
                logger.error(f'关闭订单: {result}')
                await order.close()  # 支付关闭
            case 'PAYERROR':
                logger.error(f'支付失败: {result}')
                await order.pay_failed()  # 支付失败

        return success()

    except DoesNotExist:
        logger.error(f'订单 "{result.out_trade_no}" 不存在')
        return fail(400, f'订单 "{result.out_trade_no}" 不存在')

    except Exception as e:
        logger.error(f'支付处理失败: {e}')
        return fail(500, '支付处理失败')


@router.post('/order/checkin')
async def order_checkin(form: CheckinForm):
    """检查订单状态"""
    user = State.get('user')
    if not user.is_adm:
        return abort(403, '没有核销权限')

    try:
        order = await Order.get(vcode=form.vcode)
        await order.checkin()
        return {'msg': '核销成功', 'order': await order.detail()}
    except DoesNotExist:
        return abort(400, '验证码错误')
    except Exception as e:
        return abort(500, f'核销失败: {e}')


@router.get('/order/list')
async def get_order_list(is_joint: bool = False, status: OrderFilter | None = None, page: int = 1):
    """获取用户订单列表"""
    try:
        # 构建查询条件
        user = State.get('user')
        condition = Q(uid=user.id)
        if status:
            condition &= status.query
        query = Order.filter(condition)
        query = query.filter(catg=Catg.joint) if is_joint else query.exclude(catg=Catg.joint)

        # 分页查询
        offset = (page - 1) * cfg.PAGE_SIZE
        orders = await query.order_by('-created').limit(cfg.PAGE_SIZE).offset(offset)
        return [await order.detail() for order in orders]

    except Exception as e:
        return abort(500, f'获取订单列表失败: {e}')


@router.get('/order/detail')
async def get_order_detail(order_id: int):
    """获取订单详情"""
    if order := await Order.get_or_none(id=order_id):
        return await order.detail()  # 获取展览信息
    else:
        return abort(404, '订单不存在')


@router.post('/order/cancel')
async def cancel_order(form: OidForm):
    """取消订单"""
    user = State.get('user')
    try:
        order = await Order.get(id=form.order_id, uid=user.id)
        await order.cancel()
        await order.prefetch('venue')
        code, message = wxpay.close(
            out_trade_no=order.trade_no,
            mchid=cfg.WX_PAYMENT.mchid,
            sub_mchid=order.venue.mchid,
        )
        logger.info(f'关闭订单 {order.trade_no} 结果: {code=}, {message=}')
        if 200 <= code < 300:
            logger.info(f'订单 {order.trade_no} 关闭成功')
            return {'msg': '订单已取消'}
        else:
            msg = message.get('errmsg') if isinstance(message, dict) else message
            return abort(code, msg or '取消订单失败')
    except DoesNotExist:
        return abort(404, '订单不存在或已删除')
    except Exception as e:
        logger.error(f'取消订单失败: {e}')
        return abort(500, f'取消订单失败: {e}')


@router.post('/order/refund')
async def refund_order(form: OidForm):
    """申请退款"""
    user = State.get('user')
    try:
        order = await Order.get(id=form.order_id, uid=user.id)
    except DoesNotExist:
        return abort(404, '订单不存在')

    try:
        # 调用微信退款接口
        await order.prefetch()
        amount = int(order.amount * 100)  # 单位：分
        code, message = wxpay.refund(
            out_refund_no=f'RF-{order.trade_no}',
            amount={'refund': amount, 'total': amount, 'currency': 'CNY'},
            transaction_id=order.wx_transaction_id,
            out_trade_no=order.trade_no,
            reason=order.refund_reason,
            funds_account='AVAILABLE',
            notify_url=cfg.REFUND_NOTIFY_URL,
            sub_mchid=order.venue.mchid,
        )
        logger.info(f'订单 {order.trade_no} 退款申请结果: {code=}, {message=}')
        message = AttrDict(message)

        if code != 200:
            if message.code == 'NOT_ENOUGH':
                return abort(400, '余额不足，请稍候再试')
            else:
                return abort(code, '退款失败')

        match message.status:
            case 'SUCCESS':
                await order.refund_success(message.refund_id, message.success_time)  # 退款成功
                return {'msg': '退款申请已提交'}
            case 'CLOSED':
                return abort(400, '退款关闭')
            case 'PROCESSING':
                await order.start_refund()  # 修改订单状态，开始退款流程
                return {'msg': '退款处理中'}
            case '_':
                return abort(400, '退款异常')

    except Exception as e:
        order.status = OrderStatus.paid  # 恢复订单状态
        await order.save()
        logger.error(f'订单 {order.trade_no} 申请退款失败: {e}')
        return abort(500, f'申请退款失败: {e}')


@router.post('/refund/callback')
async def refund_callback(request: Request):
    """退款回调"""
    # 解析回调数据
    headers = dict(request.headers)
    body = (await request.body()).decode('utf-8')
    data = wxpay.callback(headers, body)

    if not isinstance(data, dict) or 'resource' not in data:
        # 微信支付有时会故意生成错误签名，以探测商户是否正确地验证了签名，此时不用打印日志
        if not headers.get('wechatpay-signature', '').startswith('WECHATPAY/SIGNTEST'):
            logger.error(f'退款回调数据格式错误:\n\t{headers=}\n\t{body=}')
        return fail(400, '退款回调数据格式错误')

    logger.info(f'收到退款回调数据: {data}')
    result = AttrDict(data['resource'])  # type: ignore

    try:
        # 查找订单
        order = await Order.get(trade_no=result.out_trade_no)

        match result.refund_status:
            case 'SUCCESS':
                logger.info(f'订单 {result.out_trade_no} 退款成功')
                await order.refund_success(result.refund_id, result.success_time)
            case 'CLOSED':
                logger.error(f'订单 {result.out_trade_no} 退款关闭: {result}')
                await order.refund_failed('退款关闭')
            case 'ABNORMAL':
                logger.error(f'订单 {result.out_trade_no} 退款异常: {result}')
                await order.refund_failed('退款异常')

        return success()

    except DoesNotExist:
        logger.error(f'订单 {result.out_trade_no} 不存在')
        return fail(404, f'订单 {result.out_trade_no} 不存在')

    except Exception as e:
        logger.error(f'订单 {result.out_trade_no} 退款失败: {e}')
        return fail(500, '退款失败')


@router.delete('/order/delete')
async def delete_order(form: OidForm):
    """删除订单"""
    user = State.get('user')
    try:
        order = await Order.get(id=form.order_id, uid=user.id)
        await order.delete()
    except DoesNotExist:
        pass  # 订单不存在，直接返回成功
    except Exception as err:
        return abort(500, err)
    return {'msg': '订单已删除'}


@router.post('/reserve/confirm')
async def reserve_confirm(form: ReserveForm):
    """预约确认"""
    order = await Order.get(id=form.order_id)
    order.ap_time = datetime.datetime.fromisoformat(form.date)
    await order.save()
    return success()
