from enum import StrEnum

from pydantic import BaseModel, Field, field_validator
from tortoise.expressions import Q

from apps.exhi.schemas import TicketCategory as Catg


class OrderStatus(StrEnum):
    """订单状态"""

    # 未支付
    pending = '待支付'
    failed = '支付失败'
    canceled = '已取消'
    closed = '已关闭'
    # 已支付
    paid = '待使用'
    used = '已使用'
    # 退款
    refunding = '退款中'
    refunded = '已退款'


class OrderFilter(StrEnum):
    """订单过滤"""

    paid = '待使用'
    used = '已使用'
    refund = '退款'
    unpaid = '未支付'

    @property
    def query(self) -> Q:
        match self:
            case self.paid:
                return Q(status=OrderStatus.paid)
            case self.used:
                return Q(status=OrderStatus.used)
            case self.refund:
                return Q(status__in=[OrderStatus.refunding, OrderStatus.refunded])
            case self.unpaid:
                return Q(
                    status__in=[
                        OrderStatus.pending,
                        OrderStatus.failed,
                        OrderStatus.canceled,
                        OrderStatus.closed,
                    ]
                )
            case _:
                raise ValueError(f'未知的订单状态: {self}')


class OrderSubmitForm(BaseModel):
    """订单创建请求"""

    tid: int = Field(..., ge=1, description='展票ID')
    catg: Catg = Field(..., description='票种类型')
    quantity: int = Field(..., ge=1, le=100, description='购买数量')
    amount: float = Field(..., gt=0, description='订单价格')
    sub_tids: list[int] = Field([], description='子票 ID 列表(仅对联票有效)')
    timeslot: str = Field(..., description='场次')
    grade: str = Field(..., description='票档')

    @field_validator('catg')
    @classmethod
    def validate_ticket_catg(cls, v):
        if v not in Catg:
            raise ValueError('票种类型无效')
        return v

    @field_validator('sub_tids')
    @classmethod
    def validate_sub_tids(cls, v, info):
        n_sub_tids = len(v)
        if info.data.get('catg') == Catg.joint:
            if n_sub_tids < 2:
                raise ValueError('子票数量不能小于 2')
        else:
            if n_sub_tids > 0:
                raise ValueError('普通票不允许有子票 ID 列表')
        return v


class CheckinForm(BaseModel):
    """核销请求"""

    vcode: str = Field(..., max_length=64, description='订单ID')


class OidForm(BaseModel):
    order_id: int = Field(..., description='订单ID')


class ReserveForm(BaseModel):
    """预约请求"""

    order_id: int = Field(..., description='订单ID')
    date: str = Field(..., description='预约时间')
