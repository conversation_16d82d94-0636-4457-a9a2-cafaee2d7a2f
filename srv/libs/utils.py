import datetime
import hashlib
import hmac
import os
from collections.abc import Mapping
from math import ceil
from pathlib import Path
from secrets import token_hex

import aiofiles
from fastapi import UploadFile
from filetype import guess_extension
from xxhash import xxh32_hexdigest as xxh32

from config import DEBUG, DOMAIN, SECRET_KEY, TZ, UPLOAD_DIR, UPLOAD_URL

BASE_CODES = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'


def dic2str(params: Mapping | None, **kwargs):
    """将传入的kwargs按key排序，拼接成querystring格式返回"""
    params = dict(params or {})
    params.update(kwargs)
    return '&'.join(f'{key}={value}' for key, value in sorted(params.items()))


def encode(n: int, nsys=62) -> str:
    """将整数转换为指定进制字符串"""
    encoded = ''
    while n:
        n, remainder = divmod(n, nsys)
        encoded = BASE_CODES[remainder] + encoded
    return encoded


def decode(s: str, nsys=62) -> int:
    """将指定进制字符串转换为整数"""
    n = 0
    for c in s:
        n = n * nsys + BASE_CODES.index(c)
    return n


def base_encode(s: bytes, nsys=62) -> str:
    """将字节串转换为指定进制字符串"""
    return encode(int.from_bytes(s, 'big'), nsys)


def base_decode(s: str, nsys=62) -> bytes:
    """将指定进制字符串转换为字节串"""
    n = decode(s, nsys)
    n_bytes = ceil(n.bit_length() / 8)
    return n.to_bytes(n_bytes, 'big')


def confuse(string: str):
    """自混淆字符串，再次运算后还原"""
    if len(string) % 2 != 0:
        string += '\0'
    reverse_odd = string[::2][::-1]
    forward_even = string[1::2]

    confused = ''.join(x + y for x, y in zip(reverse_odd, forward_even, strict=False))
    return confused.strip('\0')


def make_signature(message: str, secret_key: bytes = SECRET_KEY, short=False):
    """
    生成信息签名

    :param message: 需要签名的信息
    :param secret_key: 密钥
    :return: 签名值
    """
    salt = token_hex(4) if short else token_hex(8)

    if short:
        sign = xxh32(message.encode('utf8') + secret_key)
    else:
        sign = hmac.new(secret_key, message.encode('utf8'), hashlib.sha256).hexdigest()

    code = int(salt + sign, 16)
    return encode(code, 62)


def verify_signature(message: str, signature: str, secret_key: bytes = SECRET_KEY, short=False):
    """
    验证信息签名

    :param message: 需要验签的信息
    :param signature: 签名值
    :param secret_key: 密钥
    :return: True if验签成功，False otherwise
    """
    start = -8 if short else -64
    original_signature = f'{decode(signature, 62):x}'[start:]

    if short:
        expected_signature = xxh32(message.encode('utf8') + secret_key)
    else:
        expected_signature = hmac.new(secret_key, message.encode('utf8'), hashlib.sha256).hexdigest()

    return original_signature == expected_signature


def verify_signatures(signature_1: str, signature_2: str, short=False):
    """验证两个签名是否相同"""
    start = -8 if short else -64
    original_1 = f'{decode(signature_1, 62):x}'[start:]
    original_2 = f'{decode(signature_2, 62):x}'[start:]
    return original_1 == original_2


def xhash(string: str | bytes) -> str:
    """字符串哈希"""
    if isinstance(string, str):
        string = string.encode('utf-8')
    return xxh32(string)


def file_hash(path: Path | str) -> str:
    """文件哈希"""
    path = Path(path) if isinstance(path, str) else path
    return xhash(path.read_bytes())


def file_type(file: UploadFile) -> str:
    """读取文件真实类型"""
    ext = guess_extension(file.file.read(2048))
    file.file.seek(0)
    return ext or 'unknown'


def file_size(file: UploadFile) -> int:
    """获取文件大小"""
    file.file.seek(0, os.SEEK_END)
    size = file.file.tell()
    file.file.seek(0)  # Seek back to the beginning
    return size


def up_url(filename: str) -> str:
    """上传文件的 URL"""
    scheme = 'http' if DEBUG else 'https'
    if filename.startswith(UPLOAD_URL):
        return f'{scheme}://{DOMAIN}{filename}'
    else:
        return f'{scheme}://{DOMAIN}{UPLOAD_URL}/{filename}'


async def save_upfile(
    file: UploadFile,
    prefix: str | None = None,
    seq: int | None = None,
    filename: str | None = None,
) -> str:
    """保存上传的文件"""
    os.makedirs(UPLOAD_DIR, exist_ok=True)

    if not filename:
        # 计算 xhash
        contents = await file.read()
        hash_value = xhash(contents)
        await file.seek(0)  # Seek back to the beginning

        prefix = prefix or 'Upload'

        # Determine filename
        ext = getattr(file, 'extension', file_type(file))
        if seq is None:
            filename = f'{prefix}{hash_value}.{ext}'
        else:
            filename = f'{prefix}{seq}_{hash_value}.{ext}'
    else:
        ext = filename.split('.')[-1]

    file_path = os.path.join(UPLOAD_DIR, filename)

    # Save file in chunks
    async with aiofiles.open(file_path, 'wb') as out_file:
        while content := await file.read(8192):
            await out_file.write(content)

    return f'{UPLOAD_URL}/{filename}'


def cn_date(date: datetime.datetime | datetime.date, accuracy='s', ast_tz=True) -> str:
    """中文日期格式化"""
    if not date:
        return ''  # 处理空值
    if type(date) is datetime.date:
        date = datetime.datetime.combine(date, datetime.datetime.min.time())  # 转换为 datetime 对象
    if ast_tz and isinstance(date, datetime.datetime):
        date = date.astimezone(TZ)  # 转换为本地时区

    if accuracy == 'd':
        return date.strftime('%Y年%m月%d日')
    elif accuracy == 'h':
        return date.strftime('%Y年%m月%d日 %H时')
    elif accuracy == 'm':
        return date.strftime('%Y年%m月%d日 %H时%M分')
    else:
        return date.strftime('%Y年%m月%d日 %H时%M分%S秒')


def iso_date(date: datetime.datetime | datetime.date, accuracy='s', ast_tz=True) -> str:
    """ISO 日期格式化"""
    if not date:
        return ''  # 处理空值
    if type(date) is datetime.date:
        date = datetime.datetime.combine(date, datetime.datetime.min.time())  # 转换为 datetime 对象
    if ast_tz and isinstance(date, datetime.datetime):
        date = date.astimezone(TZ)  # 转换为本地时区

    if accuracy == 'd':
        return date.strftime('%Y-%m-%d')
    elif accuracy == 'h':
        return date.strftime('%Y-%m-%d %H')
    elif accuracy == 'm':
        return date.strftime('%Y-%m-%d %H:%M')
    else:
        return date.strftime('%Y-%m-%d %H:%M:%S')


def format_income(income: float) -> str:
    """格式化收入显示"""
    if income >= 10000:
        return f'{income / 10000:.1f} 万元'
    return f'{income:.2f} 元'
